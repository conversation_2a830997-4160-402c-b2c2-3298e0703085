package com.swmansion.gesturehandler.core

interface GestureHandlerInteractionController {
  fun shouldWaitForHandlerFailure(handler: <PERSON>esture<PERSON><PERSON><PERSON>, otherHandler: GestureHandler): Boolean
  fun shouldRequireHandlerToWaitForFailure(handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, otherHandler: GestureHandler): Boolean
  fun shouldRecognizeSimultaneously(handler: <PERSON>esture<PERSON><PERSON><PERSON>, otherHandler: GestureHandler): Boolean
  fun shouldHandlerBeCancelledBy(handler: <PERSON>esture<PERSON><PERSON><PERSON>, otherHandler: Gesture<PERSON>and<PERSON>): <PERSON><PERSON><PERSON>
}
