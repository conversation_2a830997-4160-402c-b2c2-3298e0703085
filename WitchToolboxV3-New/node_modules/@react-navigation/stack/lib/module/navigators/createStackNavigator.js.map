{"version": 3, "names": ["createNavigatorFactory", "StackActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useLocale", "useNavigationBuilder", "React", "StackView", "jsx", "_jsx", "StackNavigator", "id", "initialRouteName", "children", "layout", "screenListeners", "screenOptions", "screenLayout", "UNSTABLE_router", "rest", "direction", "state", "describe", "descriptors", "navigation", "NavigationContent", "useEffect", "addListener", "e", "isFocused", "requestAnimationFrame", "index", "defaultPrevented", "dispatch", "popToTop", "target", "key", "createStackNavigator", "config"], "sourceRoot": "../../../src", "sources": ["navigators/createStackNavigator.tsx"], "mappings": ";;AAAA,SACEA,sBAAsB,EAKtBC,YAAY,EAEZC,WAAW,EAIXC,SAAS,EACTC,oBAAoB,QACf,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAQ9B,SAASC,SAAS,QAAQ,6BAA0B;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAErD,SAASC,cAAcA,CAAC;EACtBC,EAAE;EACFC,gBAAgB;EAChBC,QAAQ;EACRC,MAAM;EACNC,eAAe;EACfC,aAAa;EACbC,YAAY;EACZC,eAAe;EACf,GAAGC;AACgB,CAAC,EAAE;EACtB,MAAM;IAAEC;EAAU,CAAC,GAAGhB,SAAS,CAAC,CAAC;EAEjC,MAAM;IAAEiB,KAAK;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,UAAU;IAAEC;EAAkB,CAAC,GACnEpB,oBAAoB,CAMlBF,WAAW,EAAE;IACbQ,EAAE;IACFC,gBAAgB;IAChBC,QAAQ;IACRC,MAAM;IACNC,eAAe;IACfC,aAAa;IACbC,YAAY;IACZC;EACF,CAAC,CAAC;EAEJZ,KAAK,CAACoB,SAAS,CACb;EACE;EACAF,UAAU,CAACG,WAAW,GAAG,UAAU,EAAGC,CAAC,IAAK;IAC1C,MAAMC,SAAS,GAAGL,UAAU,CAACK,SAAS,CAAC,CAAC;;IAExC;IACA;IACAC,qBAAqB,CAAC,MAAM;MAC1B,IACET,KAAK,CAACU,KAAK,GAAG,CAAC,IACfF,SAAS,IACT,CAAED,CAAC,CAA2CI,gBAAgB,EAC9D;QACA;QACA;QACAR,UAAU,CAACS,QAAQ,CAAC;UAClB,GAAG/B,YAAY,CAACgC,QAAQ,CAAC,CAAC;UAC1BC,MAAM,EAAEd,KAAK,CAACe;QAChB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,EACJ,CAACZ,UAAU,EAAEH,KAAK,CAACU,KAAK,EAAEV,KAAK,CAACe,GAAG,CACrC,CAAC;EAED,oBACE3B,IAAA,CAACgB,iBAAiB;IAAAZ,QAAA,eAChBJ,IAAA,CAACF,SAAS;MAAA,GACJY,IAAI;MACRC,SAAS,EAAEA,SAAU;MACrBC,KAAK,EAAEA,KAAM;MACbC,QAAQ,EAAEA,QAAS;MACnBC,WAAW,EAAEA,WAAY;MACzBC,UAAU,EAAEA;IAAW,CACxB;EAAC,CACe,CAAC;AAExB;AAEA,OAAO,SAASa,oBAAoBA,CAmBlCC,MAAe,EAAmC;EAClD,OAAOrC,sBAAsB,CAACS,cAAc,CAAC,CAAC4B,MAAM,CAAC;AACvD", "ignoreList": []}