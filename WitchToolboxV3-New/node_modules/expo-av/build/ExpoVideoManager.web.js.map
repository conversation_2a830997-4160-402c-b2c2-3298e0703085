{"version": 3, "file": "ExpoVideoManager.web.js", "sourceRoot": "", "sources": ["../src/ExpoVideoManager.web.ts"], "names": [], "mappings": "AACA,OAAO,UAAU,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,iBAAiB,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AAE1E,eAAe;IACb,IAAI,SAAS;QACX,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,IAAI,WAAW;QACb,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,IAAI,cAAc;QAChB,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,IAAI,eAAe;QACjB,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,OAAyB,EACzB,mBAA4B;QAE5B,IAAI,mBAAmB,EAAE,CAAC;YACxB,MAAM,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,MAAM,cAAc,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC;QACD,OAAO,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;CACF,CAAC", "sourcesContent": ["import { AVPlaybackStatus } from './AV';\nimport ExponentAV from './ExponentAV';\nimport { requestFullscreen, exitFullscreen } from './FullscreenUtils.web';\n\nexport default {\n  get ScaleNone(): string {\n    return 'none';\n  },\n  get ScaleToFill(): string {\n    return 'fill';\n  },\n  get ScaleAspectFit(): string {\n    return 'contain';\n  },\n  get ScaleAspectFill(): string {\n    return 'cover';\n  },\n\n  async setFullscreen(\n    element: HTMLMediaElement,\n    isFullScreenEnabled: boolean\n  ): Promise<AVPlaybackStatus> {\n    if (isFullScreenEnabled) {\n      await requestFullscreen(element);\n    } else {\n      await exitFullscreen(element);\n    }\n    return ExponentAV.getStatusForVideo(element);\n  },\n};\n"]}