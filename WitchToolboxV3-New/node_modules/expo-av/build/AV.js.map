{"version": 3, "file": "AV.js", "sourceRoot": "", "sources": ["../src/AV.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C,OAAO,EAKL,sBAAsB,GAEvB,MAAM,YAAY,CAAC;AAEpB,YAAY;AACZ,yBAAyB;AACzB,uDAAuD;AACvD,OAAO;AACP,SAAS;AACT,mDAAmD;AACnD,wCAAwC;AACxC,iBAAiB;AAEjB;;GAEG;AACH,MAAM,CAAC,MAAM,wCAAwC,GAAW,GAAG,CAAC;AAEpE,cAAc;AACd;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,CAAC,MAAM,gCAAgC,GAA0B;IACrE,cAAc,EAAE,CAAC;IACjB,4BAA4B,EAAE,wCAAwC;IACtE,UAAU,EAAE,KAAK;IACjB,IAAI,EAAE,GAAG;IACT,kBAAkB,EAAE,KAAK;IACzB,MAAM,EAAE,GAAG;IACX,QAAQ,EAAE,CAAC;IACX,OAAO,EAAE,KAAK;IACd,SAAS,EAAE,KAAK;CACjB,CAAC;AAEF,cAAc;AACd;;GAEG;AACH,MAAM,UAAU,yBAAyB,CACvC,MAAgC;IAEhC,IAAI,GAAG,GAAkB,IAAI,CAAC;IAC9B,IAAI,mBAAmB,GAAkB,IAAI,CAAC;IAC9C,IAAI,OAA0C,CAAC;IAE/C,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;QACxD,OAAO;YACL,GAAG,EAAE,MAAM;YACX,mBAAmB;YACnB,OAAO;SACR,CAAC;IACJ,CAAC;IAED,MAAM,KAAK,GAAiB,2BAA2B,CAAC,MAAM,CAAC,CAAC;IAChE,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,GAAG,GAAG,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,GAAG,CAAC;IACpC,CAAC;SAAM,IACL,MAAM,IAAI,IAAI;QACd,OAAO,MAAM,KAAK,QAAQ;QAC1B,KAAK,IAAI,MAAM;QACf,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ,EAC9B,CAAC;QACD,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;IACnB,CAAC;IAED,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IACE,MAAM,IAAI,IAAI;QACd,OAAO,MAAM,KAAK,QAAQ;QAC1B,8BAA8B,IAAI,MAAM;QACxC,OAAO,MAAM,CAAC,4BAA4B,KAAK,QAAQ,EACvD,CAAC;QACD,mBAAmB,GAAG,MAAM,CAAC,4BAA4B,CAAC;IAC5D,CAAC;IAED,IACE,MAAM,IAAI,IAAI;QACd,OAAO,MAAM,KAAK,QAAQ;QAC1B,SAAS,IAAI,MAAM;QACnB,OAAO,MAAM,CAAC,OAAO,KAAK,QAAQ,EAClC,CAAC;QACD,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;IAC3B,CAAC;IACD,OAAO,EAAE,GAAG,EAAE,mBAAmB,EAAE,OAAO,EAAE,CAAC;AAC/C,CAAC;AAED,SAAS,2BAA2B,CAAC,MAAgC;IACnE,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,KAAK,GAAiB,IAAI,CAAC;IAC/B,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;SAAM,IAAI,MAAM,YAAY,KAAK,EAAE,CAAC;QACnC,KAAK,GAAG,MAAM,CAAC;IACjB,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,cAAc;AACd;;GAEG;AACH,MAAM,UAAU,0BAA0B,CAAC,MAA6B;IACtE,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC;QAC7E,MAAM,IAAI,UAAU,CAAC,yCAAyC,CAAC,CAAC;IAClE,CAAC;IACD,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;QAClF,MAAM,IAAI,UAAU,CAAC,0CAA0C,CAAC,CAAC;IACnE,CAAC;IACD,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC;QACzF,MAAM,IAAI,UAAU,CAAC,wCAAwC,CAAC,CAAC;IACjE,CAAC;AACH,CAAC;AAED,cAAc;AACd;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,+CAA+C,CACnE,MAA+B,EAC/B,aAA2C,EAC3C,aAAsB;IAKtB,8BAA8B;IAC9B,MAAM,iBAAiB,GACrB,aAAa,IAAI,IAAI;QACnB,CAAC,CAAC,gCAAgC;QAClC,CAAC,CAAC;YACE,GAAG,gCAAgC;YACnC,GAAG,aAAa;SACjB,CAAC;IACR,0BAA0B,CAAC,iBAAiB,CAAC,CAAC;IAE9C,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;QACxD,OAAO;YACL,YAAY,EAAE;gBACZ,GAAG,EAAE,MAAM;gBACX,mBAAmB,EAAE,IAAI;aAC1B;YACD,iBAAiB;SAClB,CAAC;IACJ,CAAC;IAED,+BAA+B;IAC/B,MAAM,KAAK,GAAG,2BAA2B,CAAC,MAAM,CAAC,CAAC;IAClD,IAAI,aAAa,IAAI,KAAK,EAAE,CAAC;QAC3B,mFAAmF;QACnF,MAAM,KAAK,CAAC,aAAa,EAAE,CAAC;IAC9B,CAAC;IAED,wBAAwB;IACxB,MAAM,YAAY,GAAkC,yBAAyB,CAAC,MAAM,CAAC,CAAC;IAEtF,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;QAC1B,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;IACzE,CAAC;IAED,gDAAgD;IAChD,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC5B,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC;IACpC,CAAC;IAED,OAAO,EAAE,YAAY,EAAE,iBAAiB,EAAE,CAAC;AAC7C,CAAC;AAED,cAAc;AACd;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,QAAuB,IAAI;IAC3D,OAAO;QACL,QAAQ,EAAE,KAAK;QACf,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;KAC9B,CAAC;AACJ,CAAC;AA8ID;;;;GAIG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG;IAC3B,KAAK,CAAC,SAAS;QACb,OAAQ,IAAwB,CAAC,cAAc,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;IACxE,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,cAAsB,EACtB,aAAkC,EAAE;QAEpC,OAAQ,IAAwB,CAAC,cAAc,CAAC;YAC9C,cAAc;YACd,UAAU,EAAE,IAAI;YAChB,wBAAwB,EAAE,UAAU,CAAC,oBAAoB;YACzD,yBAAyB,EAAE,UAAU,CAAC,qBAAqB;SAC5D,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACd,OAAQ,IAAwB,CAAC,cAAc,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;IACzE,CAAC;IAED,KAAK,CAAC,SAAS;QACb,OAAQ,IAAwB,CAAC,cAAc,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;IAC5F,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,cAAsB,EACtB,aAAkC,EAAE;QAEpC,OAAQ,IAAwB,CAAC,cAAc,CAAC;YAC9C,cAAc;YACd,wBAAwB,EAAE,UAAU,CAAC,oBAAoB;YACzD,yBAAyB,EAAE,UAAU,CAAC,qBAAqB;SAC5D,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,IAAY,EACZ,qBAA8B,KAAK,EACnC,yBAAiD,sBAAsB,CAAC,MAAM;QAE9E,OAAQ,IAAwB,CAAC,cAAc,CAAC;YAC9C,IAAI;YACJ,kBAAkB;YAClB,sBAAsB;SACvB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,QAAiB;QACpD,OAAQ,IAAwB,CAAC,cAAc,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;IACxE,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAAgB;QACpC,OAAQ,IAAwB,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,SAAkB;QACxC,OAAQ,IAAwB,CAAC,cAAc,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;IACjE,CAAC;IAED,KAAK,CAAC,8BAA8B,CAClC,4BAAoC;QAEpC,OAAQ,IAAwB,CAAC,cAAc,CAAC,EAAE,4BAA4B,EAAE,CAAC,CAAC;IACpF,CAAC;CACF,CAAC;AAEF,cAAc,YAAY,CAAC", "sourcesContent": ["import { Asset } from 'expo-asset';\nimport { Platform } from 'expo-modules-core';\n\nimport {\n  AVPlaybackSource,\n  AVPlaybackNativeSource,\n  AVPlaybackStatus,\n  AVPlaybackStatusToSet,\n  PitchCorrectionQuality,\n  AVPlaybackTolerance,\n} from './AV.types';\n\n// TODO add:\n//  disableFocusOnAndroid\n//  audio routes (at least did become noisy on android)\n//  pan\n//  pitch\n//  API to explicitly request audio focus / session\n//  API to select stream type on Android\n//  subtitles API\n\n/**\n * @hidden\n */\nexport const _DEFAULT_PROGRESS_UPDATE_INTERVAL_MILLIS: number = 500;\n\n// @needsAudit\n/**\n * The default initial `AVPlaybackStatusToSet` of all `Audio.Sound` objects and `Video` components is as follows:\n *\n * ```javascript\n * {\n *   progressUpdateIntervalMillis: 500,\n *   positionMillis: 0,\n *   shouldPlay: false,\n *   rate: 1.0,\n *   shouldCorrectPitch: false,\n *   volume: 1.0,\n *   isMuted: false,\n *   isLooping: false,\n * }\n * ```\n *\n * This default initial status can be overwritten by setting the optional `initialStatus` in `loadAsync()` or `Audio.Sound.createAsync()`.\n */\nexport const _DEFAULT_INITIAL_PLAYBACK_STATUS: AVPlaybackStatusToSet = {\n  positionMillis: 0,\n  progressUpdateIntervalMillis: _DEFAULT_PROGRESS_UPDATE_INTERVAL_MILLIS,\n  shouldPlay: false,\n  rate: 1.0,\n  shouldCorrectPitch: false,\n  volume: 1.0,\n  audioPan: 0,\n  isMuted: false,\n  isLooping: false,\n};\n\n// @needsAudit\n/**\n * @hidden\n */\nexport function getNativeSourceFromSource(\n  source?: AVPlaybackSource | null\n): AVPlaybackNativeSource | null {\n  let uri: string | null = null;\n  let overridingExtension: string | null = null;\n  let headers: AVPlaybackNativeSource['headers'];\n\n  if (typeof source === 'string' && Platform.OS === 'web') {\n    return {\n      uri: source,\n      overridingExtension,\n      headers,\n    };\n  }\n\n  const asset: Asset | null = _getAssetFromPlaybackSource(source);\n  if (asset != null) {\n    uri = asset.localUri || asset.uri;\n  } else if (\n    source != null &&\n    typeof source !== 'number' &&\n    'uri' in source &&\n    typeof source.uri === 'string'\n  ) {\n    uri = source.uri;\n  }\n\n  if (uri == null) {\n    return null;\n  }\n\n  if (\n    source != null &&\n    typeof source !== 'number' &&\n    'overrideFileExtensionAndroid' in source &&\n    typeof source.overrideFileExtensionAndroid === 'string'\n  ) {\n    overridingExtension = source.overrideFileExtensionAndroid;\n  }\n\n  if (\n    source != null &&\n    typeof source !== 'number' &&\n    'headers' in source &&\n    typeof source.headers === 'object'\n  ) {\n    headers = source.headers;\n  }\n  return { uri, overridingExtension, headers };\n}\n\nfunction _getAssetFromPlaybackSource(source?: AVPlaybackSource | null): Asset | null {\n  if (source == null) {\n    return null;\n  }\n\n  let asset: Asset | null = null;\n  if (typeof source === 'number') {\n    asset = Asset.fromModule(source);\n  } else if (source instanceof Asset) {\n    asset = source;\n  }\n  return asset;\n}\n\n// @needsAudit\n/**\n * @hidden\n */\nexport function assertStatusValuesInBounds(status: AVPlaybackStatusToSet): void {\n  if (typeof status.rate === 'number' && (status.rate < 0 || status.rate > 32)) {\n    throw new RangeError('Rate value must be between 0.0 and 32.0');\n  }\n  if (typeof status.volume === 'number' && (status.volume < 0 || status.volume > 1)) {\n    throw new RangeError('Volume value must be between 0.0 and 1.0');\n  }\n  if (typeof status.audioPan === 'number' && (status.audioPan < -1 || status.audioPan > 1)) {\n    throw new RangeError('Pan value must be between -1.0 and 1.0');\n  }\n}\n\n// @needsAudit\n/**\n * @hidden\n */\nexport async function getNativeSourceAndFullInitialStatusForLoadAsync(\n  source: AVPlaybackSource | null,\n  initialStatus: AVPlaybackStatusToSet | null,\n  downloadFirst: boolean\n): Promise<{\n  nativeSource: AVPlaybackNativeSource;\n  fullInitialStatus: AVPlaybackStatusToSet;\n}> {\n  // Get the full initial status\n  const fullInitialStatus: AVPlaybackStatusToSet =\n    initialStatus == null\n      ? _DEFAULT_INITIAL_PLAYBACK_STATUS\n      : {\n          ..._DEFAULT_INITIAL_PLAYBACK_STATUS,\n          ...initialStatus,\n        };\n  assertStatusValuesInBounds(fullInitialStatus);\n\n  if (typeof source === 'string' && Platform.OS === 'web') {\n    return {\n      nativeSource: {\n        uri: source,\n        overridingExtension: null,\n      },\n      fullInitialStatus,\n    };\n  }\n\n  // Download first if necessary.\n  const asset = _getAssetFromPlaybackSource(source);\n  if (downloadFirst && asset) {\n    // TODO we can download remote uri too once @nikki93 has integrated this into Asset\n    await asset.downloadAsync();\n  }\n\n  // Get the native source\n  const nativeSource: AVPlaybackNativeSource | null = getNativeSourceFromSource(source);\n\n  if (nativeSource === null) {\n    throw new Error(`Cannot load an AV asset from a null playback source`);\n  }\n\n  // If asset has been downloaded use the localUri\n  if (asset && asset.localUri) {\n    nativeSource.uri = asset.localUri;\n  }\n\n  return { nativeSource, fullInitialStatus };\n}\n\n// @needsAudit\n/**\n * @hidden\n */\nexport function getUnloadedStatus(error: string | null = null): AVPlaybackStatus {\n  return {\n    isLoaded: false,\n    ...(error ? { error } : null),\n  };\n}\n\n// @needsAudit\nexport interface AV {\n  /**\n   * Sets a new `AVPlaybackStatusToSet` on the `playbackObject`. This method can only be called if the media has been loaded.\n   * @param status The new `AVPlaybackStatusToSet` of the `playbackObject`, whose values will override the current playback status.\n   * @return A `Promise` that is fulfilled with the `AVPlaybackStatus` of the `playbackObject` once the new status has been set successfully,\n   * or rejects if setting the new status failed. See below for details on `AVPlaybackStatus`.\n   */\n  setStatusAsync(status: AVPlaybackStatusToSet): Promise<AVPlaybackStatus>;\n\n  /**\n   * Gets the `AVPlaybackStatus` of the `playbackObject`.\n   * @return A `Promise` that is fulfilled with the `AVPlaybackStatus` of the `playbackObject`.\n   */\n  getStatusAsync(): Promise<AVPlaybackStatus>;\n}\n\n// @needsAudit\n/**\n * On the `playbackObject` reference, the following API is provided.\n */\nexport interface Playback extends AV {\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ shouldPlay: true })`.\n   *\n   * Playback may not start immediately after calling this function for reasons such as buffering. Make sure to update your UI based\n   * on the `isPlaying` and `isBuffering` properties of the `AVPlaybackStatus`.\n   */\n  playAsync(): Promise<AVPlaybackStatus>;\n\n  /**\n   * Loads the media from `source` into memory and prepares it for playing. This must be called before calling `setStatusAsync()`\n   * or any of the convenience set status methods. This method can only be called if the `playbackObject` is in an unloaded state.\n   * @param source The source of the media.\n   * @param initialStatus The initial intended `AVPlaybackStatusToSet` of the `playbackObject`, whose values will override the default initial playback status.\n   * This value defaults to `{}` if no parameter is passed. For more information see the details on `AVPlaybackStatusToSet` type\n   * and the default initial playback status.\n   * @param downloadAsync If set to `true`, the system will attempt to download the resource to the device before loading.\n   * This value defaults to `true`. Note that at the moment, this will only work for `source`s of the form `require('path/to/file')` or `Asset` objects.\n   * @return A `Promise` that is fulfilled with the `AVPlaybackStatus` of the `playbackObject` once it is loaded, or rejects if loading failed.\n   * The `Promise` will also reject if the `playbackObject` was already loaded. See below for details on `AVPlaybackStatus`.\n   */\n  loadAsync(\n    source: AVPlaybackSource,\n    initialStatus?: AVPlaybackStatusToSet,\n    downloadAsync?: boolean\n  ): Promise<AVPlaybackStatus>;\n\n  /**\n   * Unloads the media from memory. `loadAsync()` must be called again in order to be able to play the media.\n   * > This cleanup function will be automatically called in the `Video` component's `componentWillUnmount`.\n   * @return A `Promise` that is fulfilled with the `AVPlaybackStatus` of the `playbackObject` once it is unloaded, or rejects if unloading failed.\n   */\n  unloadAsync(): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ shouldPlay: true, positionMillis, seekMillisToleranceAfter: tolerances.seekMillisToleranceAfter, seekMillisToleranceBefore: tolerances.seekMillisToleranceBefore })`.\n   *\n   * Playback may not start immediately after calling this function for reasons such as buffering. Make sure to update your UI based\n   * on the `isPlaying` and `isBuffering` properties of the `AVPlaybackStatus`.\n   * @param positionMillis The desired position of playback in milliseconds.\n   * @param tolerances The tolerances are used only on iOS ([more details](#what-is-seek-tolerance-and-why-would-i-want-to-use-it)).\n   */\n  playFromPositionAsync(\n    positionMillis: number,\n    tolerances?: AVPlaybackTolerance\n  ): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ shouldPlay: false })`.\n   */\n  pauseAsync(): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ shouldPlay: false, positionMillis: 0 })`.\n   */\n  stopAsync(): Promise<AVPlaybackStatus>;\n\n  /**\n   * Replays the playback item. When using `playFromPositionAsync(0)` the item is seeked to the position at `0 ms`.\n   * On iOS this method uses internal implementation of the player and is able to play the item from the beginning immediately.\n   * @param status The new `AVPlaybackStatusToSet` of the `playbackObject`, whose values will override the current playback status.\n   * `positionMillis` and `shouldPlay` properties will be overridden with respectively `0` and `true`.\n   * @return A `Promise` that is fulfilled with the `AVPlaybackStatus` of the `playbackObject` once the new status has been set successfully,\n   * or rejects if setting the new status failed.\n   */\n  replayAsync(status: AVPlaybackStatusToSet): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ positionMillis })`.\n   * @param positionMillis The desired position of playback in milliseconds.\n   * @param tolerances The tolerances are used only on iOS ([more details](#what-is-seek-tolerance-and-why-would-i-want-to-use-it)).\n   */\n  setPositionAsync(\n    positionMillis: number,\n    tolerances?: AVPlaybackTolerance\n  ): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ rate, shouldCorrectPitch, pitchCorrectionQuality })`.\n   * @param rate The desired playback rate of the media. This value must be between `0.0` and `32.0`. Only available on Android API version 23 and later and iOS.\n   * @param shouldCorrectPitch A boolean describing if we should correct the pitch for a changed rate. If set to `true`, the pitch of the audio will be corrected\n   * (so a rate different than `1.0` will timestretch the audio).\n   * @param pitchCorrectionQuality iOS time pitch algorithm setting, defaults to `Audio.PitchCorrectionQuality.Medium`.\n   * Using `Audio.PitchCorrectionQuality.Low` may cause automatic playback rate changes on iOS >= 17, as `AVAudioTimePitchAlgorithmLowQualityZeroLatency` is deprecated.\n   */\n  setRateAsync(\n    rate: number,\n    shouldCorrectPitch: boolean,\n    pitchCorrectionQuality?: PitchCorrectionQuality\n  ): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ volume, audioPan })`.\n   * Note: `audioPan` is currently only supported on Android using `androidImplementation: 'MediaPlayer'`\n   * @param volume A number between `0.0` (silence) and `1.0` (maximum volume).\n   * @param audioPan A number between `-1.0` (full left) and `1.0` (full right).\n   */\n  setVolumeAsync(volume: number, audioPan?: number): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ isMuted })`.\n   * @param isMuted A boolean describing if the audio of this media should be muted.\n   */\n  setIsMutedAsync(isMuted: boolean): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ isLooping })`.\n   * @param isLooping A boolean describing if the media should play once (`false`) or loop indefinitely (`true`).\n   */\n  setIsLoopingAsync(isLooping: boolean): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ progressUpdateIntervalMillis })`.\n   * @param progressUpdateIntervalMillis The new minimum interval in milliseconds between calls of `onPlaybackStatusUpdate`.\n   * See `setOnPlaybackStatusUpdate()` for details.\n   */\n  setProgressUpdateIntervalAsync(progressUpdateIntervalMillis: number): Promise<AVPlaybackStatus>;\n}\n\n/**\n * @hidden\n * A mixin that defines common playback methods for A/V classes, so they implement the `Playback`\n * interface.\n */\nexport const PlaybackMixin = {\n  async playAsync(): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({ shouldPlay: true });\n  },\n\n  async playFromPositionAsync(\n    positionMillis: number,\n    tolerances: AVPlaybackTolerance = {}\n  ): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({\n      positionMillis,\n      shouldPlay: true,\n      seekMillisToleranceAfter: tolerances.toleranceMillisAfter,\n      seekMillisToleranceBefore: tolerances.toleranceMillisBefore,\n    });\n  },\n\n  async pauseAsync(): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({ shouldPlay: false });\n  },\n\n  async stopAsync(): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({ positionMillis: 0, shouldPlay: false });\n  },\n\n  async setPositionAsync(\n    positionMillis: number,\n    tolerances: AVPlaybackTolerance = {}\n  ): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({\n      positionMillis,\n      seekMillisToleranceAfter: tolerances.toleranceMillisAfter,\n      seekMillisToleranceBefore: tolerances.toleranceMillisBefore,\n    });\n  },\n\n  async setRateAsync(\n    rate: number,\n    shouldCorrectPitch: boolean = false,\n    pitchCorrectionQuality: PitchCorrectionQuality = PitchCorrectionQuality.Medium\n  ): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({\n      rate,\n      shouldCorrectPitch,\n      pitchCorrectionQuality,\n    });\n  },\n\n  async setVolumeAsync(volume: number, audioPan?: number): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({ volume, audioPan });\n  },\n\n  async setIsMutedAsync(isMuted: boolean): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({ isMuted });\n  },\n\n  async setIsLoopingAsync(isLooping: boolean): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({ isLooping });\n  },\n\n  async setProgressUpdateIntervalAsync(\n    progressUpdateIntervalMillis: number\n  ): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({ progressUpdateIntervalMillis });\n  },\n};\n\nexport * from './AV.types';\n"]}