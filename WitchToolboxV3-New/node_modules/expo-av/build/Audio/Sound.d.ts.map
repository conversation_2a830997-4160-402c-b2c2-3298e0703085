{"version": 3, "file": "Sound.d.ts", "sourceRoot": "", "sources": ["../../src/Audio/Sound.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAiC,MAAM,mBAAmB,CAAC;AAGtF,OAAO,EACL,QAAQ,EAER,gBAAgB,EAChB,UAAU,EACV,gBAAgB,EAChB,qBAAqB,EAIrB,mBAAmB,EACpB,MAAM,OAAO,CAAC;AACf,OAAO,EAAE,sBAAsB,EAAE,MAAM,UAAU,CAAC;AAIlD,MAAM,MAAM,YAAY,GAAG;IACzB;;OAEG;IACH,MAAM,EAAE,MAAM,EAAE,CAAC;CAClB,CAAC;AAGF;;;;GAIG;AACH,MAAM,MAAM,WAAW,GAAG;IACxB;;;OAGG;IACH,QAAQ,EAAE,YAAY,EAAE,CAAC;IACzB;;;OAGG;IACH,SAAS,EAAE,MAAM,CAAC;CACnB,CAAC;AAGF,MAAM,MAAM,WAAW,GAAG;IACxB;;OAEG;IACH,KAAK,EAAE,KAAK,CAAC;IACb;;OAEG;IACH,MAAM,EAAE,gBAAgB,CAAC;CAC1B,CAAC;AAEF,KAAK,aAAa,GAAG,MAAM,GAAG,gBAAgB,GAAG,IAAI,CAAC;AAGtD,MAAM,MAAM,mBAAmB,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC;AASzE;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,qBAAa,KAAM,YAAW,QAAQ;IACpC,OAAO,EAAE,OAAO,CAAS;IACzB,QAAQ,EAAE,OAAO,CAAS;IAC1B,IAAI,EAAE,aAAa,CAAQ;IAC3B,iBAAiB,EAAE,MAAM,GAAG,IAAI,CAAQ;IACxC,qBAAqB,EAAE,IAAI,GAAG,IAAI,CAAQ;IAC1C,cAAc,EAAE;QAAE,MAAM,EAAE,MAAM,IAAI,CAAA;KAAE,EAAE,CAAM;IAC9C,aAAa,qBAAsC;IACnD,8BAA8B,EAAE,MAAM,CAAO;IAC7C,uBAAuB,EAAE,CAAC,CAAC,MAAM,EAAE,gBAAgB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAQ;IAC5E,iBAAiB,EAAE,CAAC,CAAC,QAAQ,EAAE,UAAU,KAAK,IAAI,CAAC,GAAG,IAAI,CAAQ;IAClE,sBAAsB,EAAE,mBAAmB,CAAQ;IAEnD,oDAAoD;IACpD,MAAM,CAAC,MAAM,GACX,QAAQ,gBAAgB,EACxB,gBAAe,qBAA0B,EACzC,yBAAwB,CAAC,CAAC,MAAM,EAAE,gBAAgB,KAAK,IAAI,CAAC,GAAG,IAAW,EAC1E,gBAAe,OAAc,KAC5B,OAAO,CAAC,WAAW,CAAC,CAKrB;IAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2CG;IACH,MAAM,CAAC,WAAW,GAChB,QAAQ,gBAAgB,EACxB,gBAAe,qBAA0B,EACzC,yBAAwB,CAAC,CAAC,MAAM,EAAE,gBAAgB,KAAK,IAAI,CAAC,GAAG,IAAW,EAC1E,gBAAe,OAAc,KAC5B,OAAO,CAAC,WAAW,CAAC,CAKrB;IAIF,uCAAuC,CAAC,MAAM,EAAE,gBAAgB;IAa1D,qCAAqC,CACzC,SAAS,EAAE,MAAM,OAAO,CAAC,gBAAgB,CAAC,GACzC,OAAO,CAAC,gBAAgB,CAAC;IAW5B,OAAO,CAAC,kCAAkC;IA4B1C,6BAA6B,GAAI,kBAG9B;QACD,GAAG,EAAE,aAAa,CAAC;QACnB,MAAM,EAAE,gBAAgB,CAAC;KAC1B,UAIC;IAEF,+BAA+B,GAAI,oBAGhC;QACD,GAAG,EAAE,aAAa,CAAC;QACnB,QAAQ,EAAE,UAAU,CAAC;KACtB,UAIC;IAEF,sBAAsB,GAAI,gBAAgB;QAAE,GAAG,EAAE,aAAa,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,UAI7E;IAGF,wBAAwB;IAgBxB,mBAAmB;IAKnB,cAAc,GAAI,OAAO,MAAM,UAK7B;IAOF,cAAc,QAAa,OAAO,CAAC,gBAAgB,CAAC,CASlD;IAEF;;;;;;;;;;;OAWG;IACH,yBAAyB,CAAC,sBAAsB,EAAE,CAAC,CAAC,MAAM,EAAE,gBAAgB,KAAK,IAAI,CAAC,GAAG,IAAI;IAK7F;;;;OAIG;IACH,mBAAmB,CAAC,gBAAgB,EAAE,CAAC,QAAQ,EAAE,UAAU,KAAK,IAAI;IAIpE;;;OAGG;IACH,wBAAwB,CAAC,QAAQ,EAAE,mBAAmB;IAShD,SAAS,CACb,MAAM,EAAE,gBAAgB,EACxB,aAAa,GAAE,qBAA0B,EACzC,aAAa,GAAE,OAAc,GAC5B,OAAO,CAAC,gBAAgB,CAAC;IAmCtB,WAAW,IAAI,OAAO,CAAC,gBAAgB,CAAC;IAgBxC,cAAc,CAAC,MAAM,EAAE,qBAAqB,GAAG,OAAO,CAAC,gBAAgB,CAAC;IAOxE,WAAW,CAAC,MAAM,GAAE,qBAA0B,GAAG,OAAO,CAAC,gBAAgB,CAAC;IAehF,SAAS,EAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC5C,qBAAqB,EAAG,CACtB,cAAc,EAAE,MAAM,EACtB,UAAU,CAAC,EAAE,mBAAmB,KAC7B,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC/B,UAAU,EAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC7C,SAAS,EAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC5C,gBAAgB,EAAG,CACjB,cAAc,EAAE,MAAM,EACtB,UAAU,CAAC,EAAE,mBAAmB,KAC7B,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC/B,YAAY,EAAG,CACb,IAAI,EAAE,MAAM,EACZ,kBAAkB,EAAE,OAAO,EAC3B,sBAAsB,CAAC,EAAE,sBAAsB,KAC5C,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC/B,cAAc,EAAG,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,KAAK,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAClF,eAAe,EAAG,CAAC,OAAO,EAAE,OAAO,KAAK,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAClE,iBAAiB,EAAG,CAAC,SAAS,EAAE,OAAO,KAAK,OAAO,CAAC,gBAAgB,CAAC,CAAC;IACtE,8BAA8B,EAAG,CAC/B,4BAA4B,EAAE,MAAM,KACjC,OAAO,CAAC,gBAAgB,CAAC,CAAC;CAChC"}