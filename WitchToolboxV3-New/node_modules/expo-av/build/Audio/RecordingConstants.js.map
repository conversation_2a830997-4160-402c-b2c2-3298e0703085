{"version": 3, "file": "RecordingConstants.js", "sourceRoot": "", "sources": ["../../src/Audio/RecordingConstants.ts"], "names": [], "mappings": "AAEA;;;GAGG;AACH,MAAM,CAAN,IAAY,mBAkCX;AAlCD,WAAY,mBAAmB;IAC7B,mEAAW,CAAA;IACX;;OAEG;IACH,uEAAa,CAAA;IACb;;OAEG;IACH,iEAAU,CAAA;IACV;;OAEG;IACH,iEAAU,CAAA;IACV;;OAEG;IACH,iEAAU,CAAA;IACV,eAAe;IACf,qEAAY,CAAA;IACZ;;OAEG;IACH,qEAAY,CAAA;IACZ,eAAe;IACf,mEAAW,CAAA;IACX;;OAEG;IACH,mEAAW,CAAA;IACX;;OAEG;IACH,6DAAQ,CAAA;AACV,CAAC,EAlCW,mBAAmB,KAAnB,mBAAmB,QAkC9B;AAED;;;GAGG;AACH,MAAM,CAAN,IAAY,mBAsBX;AAtBD,WAAY,mBAAmB;IAC7B,mEAAW,CAAA;IACX;;OAEG;IACH,iEAAU,CAAA;IACV;;OAEG;IACH,iEAAU,CAAA;IACV;;OAEG;IACH,2DAAO,CAAA;IACP;;OAEG;IACH,iEAAU,CAAA;IACV;;OAEG;IACH,mEAAW,CAAA;AACb,CAAC,EAtBW,mBAAmB,KAAnB,mBAAmB,QAsB9B;AAED,eAAe;AACf;;;;;;;GAOG;AACH,MAAM,CAAN,IAAY,eAmCX;AAnCD,WAAY,eAAe;IACzB,qCAAkB,CAAA;IAClB,+BAAY,CAAA;IACZ,oCAAmB,CAAA;IACnB,qCAAkB,CAAA;IAClB,oCAAiB,CAAA;IACjB,qCAAkB,CAAA;IAClB,qCAAkB,CAAA;IAClB,uCAAoB,CAAA;IACpB,iCAAc,CAAA;IACd,iCAAc,CAAA;IACd,gCAAa,CAAA;IACb,gCAAa,CAAA;IACb,mCAAgB,CAAA;IAChB,oCAAiB,CAAA;IACjB,oCAAiB,CAAA;IACjB,sCAAmB,CAAA;IACnB,sCAAmB,CAAA;IACnB,sCAAmB,CAAA;IACnB,yCAAsB,CAAA;IACtB,uCAAoB,CAAA;IACpB,uCAAoB,CAAA;IACpB,wCAAqB,CAAA;IACrB,4CAAyB,CAAA;IACzB,2CAAwB,CAAA;IACxB,0CAAuB,CAAA;IACvB,4CAAyB,CAAA;IACzB,+BAAY,CAAA;IACZ,kCAAe,CAAA;IACf,mCAAgB,CAAA;IAChB,gCAAa,CAAA;IACb,4EAAwB,CAAA;IACxB,8EAAyB,CAAA;IACzB,gCAAa,CAAA;IACb,uCAAoB,CAAA;AACtB,CAAC,EAnCW,eAAe,KAAf,eAAe,QAmC1B;AAED,eAAe;AACf;;GAEG;AACH,MAAM,CAAN,IAAY,eAMX;AAND,WAAY,eAAe;IACzB,mDAAO,CAAA;IACP,oDAAU,CAAA;IACV,0DAAa,CAAA;IACb,sDAAW,CAAA;IACX,qDAAU,CAAA;AACZ,CAAC,EANW,eAAe,KAAf,eAAe,QAM1B;AAED,eAAe;AACf;;GAEG;AACH,MAAM,CAAN,IAAY,kBAKX;AALD,WAAY,kBAAkB;IAC5B,mEAAY,CAAA;IACZ,qFAAqB,CAAA;IACrB,2FAAwB,CAAA;IACxB,mEAAY,CAAA;AACd,CAAC,EALW,kBAAkB,KAAlB,kBAAkB,QAK7B;AAED,uEAAuE;AAEvE,MAAM,YAAY,GAAqB;IACrC,iBAAiB,EAAE,IAAI;IACvB,OAAO,EAAE;QACP,SAAS,EAAE,MAAM;QACjB,YAAY,EAAE,mBAAmB,CAAC,MAAM;QACxC,YAAY,EAAE,mBAAmB,CAAC,GAAG;QACrC,UAAU,EAAE,KAAK;QACjB,gBAAgB,EAAE,CAAC;QACnB,OAAO,EAAE,MAAM;KAChB;IACD,GAAG,EAAE;QACH,SAAS,EAAE,MAAM;QACjB,YAAY,EAAE,eAAe,CAAC,QAAQ;QACtC,YAAY,EAAE,eAAe,CAAC,GAAG;QACjC,UAAU,EAAE,KAAK;QACjB,gBAAgB,EAAE,CAAC;QACnB,OAAO,EAAE,MAAM;QACf,iBAAiB,EAAE,EAAE;QACrB,oBAAoB,EAAE,KAAK;QAC3B,gBAAgB,EAAE,KAAK;KACxB;IACD,GAAG,EAAE;QACH,QAAQ,EAAE,YAAY;QACtB,aAAa,EAAE,MAAM;KACtB;CACF,CAAC;AAEF,MAAM,WAAW,GAAqB;IACpC,iBAAiB,EAAE,IAAI;IACvB,OAAO,EAAE;QACP,SAAS,EAAE,MAAM;QACjB,YAAY,EAAE,mBAAmB,CAAC,SAAS;QAC3C,YAAY,EAAE,mBAAmB,CAAC,MAAM;QACxC,UAAU,EAAE,KAAK;QACjB,gBAAgB,EAAE,CAAC;QACnB,OAAO,EAAE,MAAM;KAChB;IACD,GAAG,EAAE;QACH,SAAS,EAAE,MAAM;QACjB,YAAY,EAAE,eAAe,CAAC,GAAG;QACjC,YAAY,EAAE,eAAe,CAAC,QAAQ;QACtC,UAAU,EAAE,KAAK;QACjB,gBAAgB,EAAE,CAAC;QACnB,OAAO,EAAE,KAAK;QACd,iBAAiB,EAAE,EAAE;QACrB,oBAAoB,EAAE,KAAK;QAC3B,gBAAgB,EAAE,KAAK;KACxB;IACD,GAAG,EAAE;QACH,QAAQ,EAAE,YAAY;QACtB,aAAa,EAAE,MAAM;KACtB;CACF,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6DG;AACH,MAAM,CAAC,MAAM,uBAAuB,GAAqC;IACvE,YAAY;IACZ,WAAW;CACZ,CAAC", "sourcesContent": ["import type { RecordingOptions } from './Recording.types';\n\n/**\n * Defines the output format.\n * @platform android\n */\nexport enum AndroidOutputFormat {\n  DEFAULT = 0,\n  /**\n   * 3GPP media file format.\n   */\n  THREE_GPP = 1,\n  /**\n   * MPEG4 media file format.\n   */\n  MPEG_4 = 2,\n  /**\n   * AMR NB file format.\n   */\n  AMR_NB = 3,\n  /**\n   * AMR WB file format.\n   */\n  AMR_WB = 4,\n  // @docsMissing\n  AAC_ADIF = 5,\n  /**\n   * AAC ADTS file format.\n   */\n  AAC_ADTS = 6,\n  // @docsMissing\n  RTP_AVP = 7,\n  /**\n   * H.264/AAC data encapsulated in MPEG2/TS.\n   */\n  MPEG2TS = 8,\n  /**\n   * VP8/VORBIS data in a WEBM container.\n   */\n  WEBM = 9,\n}\n\n/**\n * Defines the audio encoding.\n * @platform android\n */\nexport enum AndroidAudioEncoder {\n  DEFAULT = 0,\n  /**\n   * AMR (Narrowband) audio codec.\n   */\n  AMR_NB = 1,\n  /**\n   * AMR (Wideband) audio codec.\n   */\n  AMR_WB = 2,\n  /**\n   * AAC Low Complexity (AAC-LC) audio codec.\n   */\n  AAC = 3,\n  /**\n   * High Efficiency AAC (HE-AAC) audio codec.\n   */\n  HE_AAC = 4,\n  /**\n   * Enhanced Low Delay AAC (AAC-ELD) audio codec.\n   */\n  AAC_ELD = 5,\n}\n\n// @docsMissing\n/**\n * > **Note:** Not all of the iOS formats included in this list of constants are currently supported by iOS,\n * > in spite of appearing in the Apple source code. For an accurate list of formats supported by iOS, see\n * > [Core Audio Codecs](https://developer.apple.com/library/content/documentation/MusicAudio/Conceptual/CoreAudioOverview/CoreAudioEssentials/CoreAudioEssentials.html)\n * > and [iPhone Audio File Formats](https://developer.apple.com/library/content/documentation/MusicAudio/Conceptual/CoreAudioOverview/CoreAudioEssentials/CoreAudioEssentials.html).\n *\n * @platform ios\n */\nexport enum IOSOutputFormat {\n  LINEARPCM = 'lpcm',\n  AC3 = 'ac-3',\n  '60958AC3' = 'cac3',\n  APPLEIMA4 = 'ima4',\n  MPEG4AAC = 'aac ',\n  MPEG4CELP = 'celp',\n  MPEG4HVXC = 'hvxc',\n  MPEG4TWINVQ = 'twvq',\n  MACE3 = 'MAC3',\n  MACE6 = 'MAC6',\n  ULAW = 'ulaw',\n  ALAW = 'alaw',\n  QDESIGN = 'QDMC',\n  QDESIGN2 = 'QDM2',\n  QUALCOMM = 'Qclp',\n  MPEGLAYER1 = '.mp1',\n  MPEGLAYER2 = '.mp2',\n  MPEGLAYER3 = '.mp3',\n  APPLELOSSLESS = 'alac',\n  MPEG4AAC_HE = 'aach',\n  MPEG4AAC_LD = 'aacl',\n  MPEG4AAC_ELD = 'aace',\n  MPEG4AAC_ELD_SBR = 'aacf',\n  MPEG4AAC_ELD_V2 = 'aacg',\n  MPEG4AAC_HE_V2 = 'aacp',\n  MPEG4AAC_SPATIAL = 'aacs',\n  AMR = 'samr',\n  AMR_WB = 'sawb',\n  AUDIBLE = 'AUDB',\n  ILBC = 'ilbc',\n  DVIINTELIMA = 0x6d730011,\n  MICROSOFTGSM = 0x6d730031,\n  AES3 = 'aes3',\n  ENHANCEDAC3 = 'ec-3',\n}\n\n// @docsMissing\n/**\n * @platform ios\n */\nexport enum IOSAudioQuality {\n  MIN = 0,\n  LOW = 0x20,\n  MEDIUM = 0x40,\n  HIGH = 0x60,\n  MAX = 0x7f,\n}\n\n// @docsMissing\n/**\n * @platform ios\n */\nexport enum IOSBitRateStrategy {\n  CONSTANT = 0,\n  LONG_TERM_AVERAGE = 1,\n  VARIABLE_CONSTRAINED = 2,\n  VARIABLE = 3,\n}\n\n// TODO : maybe make presets for music and speech, or lossy / lossless.\n\nconst HIGH_QUALITY: RecordingOptions = {\n  isMeteringEnabled: true,\n  android: {\n    extension: '.m4a',\n    outputFormat: AndroidOutputFormat.MPEG_4,\n    audioEncoder: AndroidAudioEncoder.AAC,\n    sampleRate: 44100,\n    numberOfChannels: 2,\n    bitRate: 128000,\n  },\n  ios: {\n    extension: '.m4a',\n    outputFormat: IOSOutputFormat.MPEG4AAC,\n    audioQuality: IOSAudioQuality.MAX,\n    sampleRate: 44100,\n    numberOfChannels: 2,\n    bitRate: 128000,\n    linearPCMBitDepth: 16,\n    linearPCMIsBigEndian: false,\n    linearPCMIsFloat: false,\n  },\n  web: {\n    mimeType: 'audio/webm',\n    bitsPerSecond: 128000,\n  },\n};\n\nconst LOW_QUALITY: RecordingOptions = {\n  isMeteringEnabled: true,\n  android: {\n    extension: '.3gp',\n    outputFormat: AndroidOutputFormat.THREE_GPP,\n    audioEncoder: AndroidAudioEncoder.AMR_NB,\n    sampleRate: 44100,\n    numberOfChannels: 2,\n    bitRate: 128000,\n  },\n  ios: {\n    extension: '.m4a',\n    audioQuality: IOSAudioQuality.MIN,\n    outputFormat: IOSOutputFormat.MPEG4AAC,\n    sampleRate: 44100,\n    numberOfChannels: 2,\n    bitRate: 64000,\n    linearPCMBitDepth: 16,\n    linearPCMIsBigEndian: false,\n    linearPCMIsFloat: false,\n  },\n  web: {\n    mimeType: 'audio/webm',\n    bitsPerSecond: 128000,\n  },\n};\n\n/**\n * Constant which contains definitions of the two preset examples of `RecordingOptions`, as implemented in the Audio SDK.\n *\n * # `HIGH_QUALITY`\n * ```ts\n * RecordingOptionsPresets.HIGH_QUALITY = {\n *   isMeteringEnabled: true,\n *   android: {\n *     extension: '.m4a',\n *     outputFormat: AndroidOutputFormat.MPEG_4,\n *     audioEncoder: AndroidAudioEncoder.AAC,\n *     sampleRate: 44100,\n *     numberOfChannels: 2,\n *     bitRate: 128000,\n *   },\n *   ios: {\n *     extension: '.m4a',\n *     outputFormat: IOSOutputFormat.MPEG4AAC,\n *     audioQuality: IOSAudioQuality.MAX,\n *     sampleRate: 44100,\n *     numberOfChannels: 2,\n *     bitRate: 128000,\n *     linearPCMBitDepth: 16,\n *     linearPCMIsBigEndian: false,\n *     linearPCMIsFloat: false,\n *   },\n *   web: {\n *     mimeType: 'audio/webm',\n *     bitsPerSecond: 128000,\n *   },\n * };\n * ```\n *\n * # `LOW_QUALITY`\n * ```ts\n * RecordingOptionsPresets.LOW_QUALITY = {\n *   isMeteringEnabled: true,\n *   android: {\n *     extension: '.3gp',\n *     outputFormat: AndroidOutputFormat.THREE_GPP,\n *     audioEncoder: AndroidAudioEncoder.AMR_NB,\n *     sampleRate: 44100,\n *     numberOfChannels: 2,\n *     bitRate: 128000,\n *   },\n *   ios: {\n *     extension: '.caf',\n *     audioQuality: IOSAudioQuality.MIN,\n *     sampleRate: 44100,\n *     numberOfChannels: 2,\n *     bitRate: 128000,\n *     linearPCMBitDepth: 16,\n *     linearPCMIsBigEndian: false,\n *     linearPCMIsFloat: false,\n *   },\n *   web: {\n *     mimeType: 'audio/webm',\n *     bitsPerSecond: 128000,\n *   },\n * };\n * ```\n */\nexport const RecordingOptionsPresets: Record<string, RecordingOptions> = {\n  HIGH_QUALITY,\n  LOW_QUALITY,\n};\n"]}