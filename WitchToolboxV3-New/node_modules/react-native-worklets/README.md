![header](https://github.com/user-attachments/assets/41b37472-e55a-4c54-9064-31258e03870c)

### The React Native multithreading library

React Native Worklets is a library that allows you to run JavaScript code in parallel on multiple threads and runtimes.

While the library is in transitional period where we hone the API, all its features are functionally stable and ready to use in production.

## Documentation

Check out our dedicated documentation page for info about this library, API reference and more: [https://docs.swmansion.com/react-native-worklets/](https://docs.swmansion.com/react-native-worklets/)

## License

Worklets library is licensed under [The MIT License](LICENSE).

## Credits

This project has been built and is maintained thanks to the support from [Shopify](https://shopify.com), [Expo.io](https://expo.io) and [Software Mansion](https://swmansion.com)

[![shopify](https://avatars1.githubusercontent.com/u/8085?v=3&s=100 'Shopify.com')](https://shopify.com)
[![expo](https://avatars2.githubusercontent.com/u/12504344?v=3&s=100 'Expo.io')](https://expo.io)
[![swm](https://logo.swmansion.com/logo?color=white&variant=desktop&width=150&tag=react-native-reanimated-github 'Software Mansion')](https://swmansion.com)

## Community Discord

[Join the Software Mansion Community Discord](https://discord.swmansion.com) to chat about Worklets or other Software Mansion libraries.

## Worklets are created by Software Mansion

Since 2012 [Software Mansion](https://swmansion.com) is a software agency with experience in building web and mobile apps. We are Core React Native Contributors and experts in dealing with all kinds of React Native issues. We can help you build your next dream product – [Hire us](https://swmansion.com/contact/projects?utm_source=reanimated&utm_medium=readme).
