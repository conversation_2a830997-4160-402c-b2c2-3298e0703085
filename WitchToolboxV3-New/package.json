{"name": "witch-toolbox-v3", "version": "3.0.0", "description": "女巫工具箱V3 - 现代化的身心灵占卜应用", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "clear": "expo start --clear", "tunnel": "expo start --tunnel", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "submit:all": "eas submit --platform all", "publish": "expo publish", "eject": "expo eject"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.4.4", "expo": "~53.0.20", "expo-av": "^15.1.7", "expo-blur": "^14.1.5", "expo-linear-gradient": "^14.1.5", "expo-print": "^14.1.4", "expo-sharing": "^13.1.5", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-reanimated": "^4.0.1", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}