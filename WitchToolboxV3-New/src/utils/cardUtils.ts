import { TarotCard, OracleCard, DivinationResult } from '../types';

// 洗牌算法
export const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

// 判断塔罗牌是否逆位
export const isCardReversed = (): boolean => {
  return Math.random() < 0.3; // 30% 概率逆位
};

// 获取塔罗牌解释
export const getTarotCardInterpretation = (card: TarotCard, isReversed: boolean = false): string => {
  const meanings = isReversed ? card.meanings.reversed : card.meanings.upright;
  const randomMeaning = meanings[Math.floor(Math.random() * meanings.length)];
  
  const position = isReversed ? '逆位' : '正位';
  return `${card.name}（${position}）：${randomMeaning}。${card.description}`;
};

// 生成塔罗牌占卜结果
export const generateTarotReading = (
  cards: TarotCard[],
  question: string = '',
  spread: string = 'single_card'
): DivinationResult => {
  const interpretations: string[] = [];
  const advice: string[] = [];
  
  cards.forEach((card, index) => {
    const isReversed = isCardReversed();
    const interpretation = getTarotCardInterpretation(card, isReversed);
    interpretations.push(`第${index + 1}张牌：${interpretation}`);
    
    // 根据牌面生成建议
    const meanings = isReversed ? card.meanings.reversed : card.meanings.upright;
    const suggestion = generateAdviceFromMeaning(meanings[0]);
    advice.push(suggestion);
  });
  
  return {
    id: `tarot_${Date.now()}`,
    type: 'tarot',
    question,
    cards,
    interpretation: interpretations.join('\n\n'),
    advice: advice.join(' '),
    timestamp: new Date(),
    spread
  };
};

// 生成神谕卡占卜结果
export const generateOracleReading = (
  cards: OracleCard[],
  question: string = ''
): DivinationResult => {
  const interpretations: string[] = [];
  const advice: string[] = [];
  
  cards.forEach((card, index) => {
    interpretations.push(`${card.name}：${card.meaning}`);
    advice.push(card.guidance);
  });
  
  return {
    id: `oracle_${Date.now()}`,
    type: 'oracle',
    question,
    cards,
    interpretation: interpretations.join('\n\n'),
    advice: advice.join(' '),
    timestamp: new Date()
  };
};

// 根据含义生成建议
const generateAdviceFromMeaning = (meaning: string): string => {
  const adviceTemplates = [
    `关于"${meaning}"，建议你保持开放的心态，相信内在的智慧。`,
    `"${meaning}"提醒你要关注当下，把握现在的机会。`,
    `面对"${meaning}"的能量，建议你采取积极的行动。`,
    `"${meaning}"的出现是一个信号，提醒你要相信自己的能力。`,
    `关于"${meaning}"，重要的是要保持平衡和耐心。`
  ];
  
  return adviceTemplates[Math.floor(Math.random() * adviceTemplates.length)];
};

// 验证占卜问题
export const validateDivinationQuestion = (question: string): { isValid: boolean; message?: string } => {
  if (!question.trim()) {
    return { isValid: true }; // 空问题是允许的
  }
  
  if (question.length < 3) {
    return { isValid: false, message: '问题太短，请提供更详细的问题。' };
  }
  
  if (question.length > 200) {
    return { isValid: false, message: '问题太长，请简化你的问题。' };
  }
  
  // 检查是否包含不当内容（简单示例）
  const inappropriateWords = ['死亡', '自杀', '伤害'];
  const hasInappropriateContent = inappropriateWords.some(word => 
    question.toLowerCase().includes(word)
  );
  
  if (hasInappropriateContent) {
    return { 
      isValid: false, 
      message: '请避免询问关于死亡、伤害等负面内容的问题。占卜应该用于积极的指导。' 
    };
  }
  
  return { isValid: true };
};

// 格式化占卜结果用于分享
export const formatReadingForSharing = (result: DivinationResult): string => {
  const date = result.timestamp.toLocaleDateString('zh-CN');
  let shareText = `🔮 ${date} 的占卜结果\n\n`;
  
  if (result.question) {
    shareText += `❓ 问题：${result.question}\n\n`;
  }
  
  if (result.cards && result.cards.length > 0) {
    shareText += `🃏 抽到的牌：\n`;
    result.cards.forEach((card, index) => {
      shareText += `${index + 1}. ${card.name}\n`;
    });
    shareText += '\n';
  }
  
  shareText += `💫 解释：${result.interpretation}\n\n`;
  shareText += `🌟 建议：${result.advice}\n\n`;
  shareText += `✨ 来自女巫工具箱V3`;
  
  return shareText;
};
