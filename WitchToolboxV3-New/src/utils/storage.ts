import AsyncStorage from '@react-native-async-storage/async-storage';
import { DivinationResult, UserPreferences } from '../types';

// 存储键名常量
const STORAGE_KEYS = {
  DIVINATION_HISTORY: 'divination_history',
  USER_PREFERENCES: 'user_preferences',
  LEARNING_PROGRESS: 'learning_progress',
  PRACTICE_PROGRESS: 'practice_progress',
  FAVORITE_TOOLS: 'favorite_tools',
  DAILY_INSPIRATION_VIEWED: 'daily_inspiration_viewed',
};

// 占卜历史记录管理
export const saveDivinationResult = async (result: DivinationResult): Promise<void> => {
  try {
    const existingHistory = await getDivinationHistory();
    const updatedHistory = [result, ...existingHistory].slice(0, 50); // 只保留最近50条记录
    await AsyncStorage.setItem(STORAGE_KEYS.DIVINATION_HISTORY, JSON.stringify(updatedHistory));
  } catch (error) {
    console.error('保存占卜结果失败:', error);
  }
};

export const getDivinationHistory = async (): Promise<DivinationResult[]> => {
  try {
    const historyJson = await AsyncStorage.getItem(STORAGE_KEYS.DIVINATION_HISTORY);
    if (historyJson) {
      const history = JSON.parse(historyJson);
      // 转换时间戳为Date对象
      return history.map((result: any) => ({
        ...result,
        timestamp: new Date(result.timestamp)
      }));
    }
    return [];
  } catch (error) {
    console.error('获取占卜历史失败:', error);
    return [];
  }
};

export const deleteDivinationResult = async (resultId: string): Promise<void> => {
  try {
    const history = await getDivinationHistory();
    const updatedHistory = history.filter(result => result.id !== resultId);
    await AsyncStorage.setItem(STORAGE_KEYS.DIVINATION_HISTORY, JSON.stringify(updatedHistory));
  } catch (error) {
    console.error('删除占卜结果失败:', error);
  }
};

// 用户偏好设置管理
export const saveUserPreferences = async (preferences: UserPreferences): Promise<void> => {
  try {
    await AsyncStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(preferences));
  } catch (error) {
    console.error('保存用户偏好失败:', error);
  }
};

export const getUserPreferences = async (): Promise<UserPreferences> => {
  try {
    const preferencesJson = await AsyncStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
    if (preferencesJson) {
      return JSON.parse(preferencesJson);
    }
    // 返回默认偏好设置
    return {
      theme: 'dark',
      language: 'zh',
      notifications: true,
      soundEnabled: true,
      favoriteTools: []
    };
  } catch (error) {
    console.error('获取用户偏好失败:', error);
    return {
      theme: 'dark',
      language: 'zh',
      notifications: true,
      soundEnabled: true,
      favoriteTools: []
    };
  }
};

// 学习进度管理
export const saveLearningProgress = async (contentId: string, progress: number, completed: boolean): Promise<void> => {
  try {
    const existingProgress = await getLearningProgress();
    existingProgress[contentId] = { progress, completed, lastUpdated: new Date().toISOString() };
    await AsyncStorage.setItem(STORAGE_KEYS.LEARNING_PROGRESS, JSON.stringify(existingProgress));
  } catch (error) {
    console.error('保存学习进度失败:', error);
  }
};

export const getLearningProgress = async (): Promise<Record<string, { progress: number; completed: boolean; lastUpdated: string }>> => {
  try {
    const progressJson = await AsyncStorage.getItem(STORAGE_KEYS.LEARNING_PROGRESS);
    return progressJson ? JSON.parse(progressJson) : {};
  } catch (error) {
    console.error('获取学习进度失败:', error);
    return {};
  }
};

// 实践进度管理
export const savePracticeProgress = async (guideId: string, stepId: string, completed: boolean): Promise<void> => {
  try {
    const existingProgress = await getPracticeProgress();
    if (!existingProgress[guideId]) {
      existingProgress[guideId] = {};
    }
    existingProgress[guideId][stepId] = { completed, completedAt: new Date().toISOString() };
    await AsyncStorage.setItem(STORAGE_KEYS.PRACTICE_PROGRESS, JSON.stringify(existingProgress));
  } catch (error) {
    console.error('保存实践进度失败:', error);
  }
};

export const getPracticeProgress = async (): Promise<Record<string, Record<string, { completed: boolean; completedAt: string }>>> => {
  try {
    const progressJson = await AsyncStorage.getItem(STORAGE_KEYS.PRACTICE_PROGRESS);
    return progressJson ? JSON.parse(progressJson) : {};
  } catch (error) {
    console.error('获取实践进度失败:', error);
    return {};
  }
};

// 收藏工具管理
export const addFavoriteTool = async (toolName: string): Promise<void> => {
  try {
    const preferences = await getUserPreferences();
    if (!preferences.favoriteTools.includes(toolName)) {
      preferences.favoriteTools.push(toolName);
      await saveUserPreferences(preferences);
    }
  } catch (error) {
    console.error('添加收藏工具失败:', error);
  }
};

export const removeFavoriteTool = async (toolName: string): Promise<void> => {
  try {
    const preferences = await getUserPreferences();
    preferences.favoriteTools = preferences.favoriteTools.filter(tool => tool !== toolName);
    await saveUserPreferences(preferences);
  } catch (error) {
    console.error('移除收藏工具失败:', error);
  }
};

export const getFavoriteTools = async (): Promise<string[]> => {
  try {
    const preferences = await getUserPreferences();
    return preferences.favoriteTools;
  } catch (error) {
    console.error('获取收藏工具失败:', error);
    return [];
  }
};

// 每日灵感查看记录
export const markDailyInspirationViewed = async (date: string): Promise<void> => {
  try {
    const viewedDates = await getDailyInspirationViewedDates();
    if (!viewedDates.includes(date)) {
      viewedDates.push(date);
      // 只保留最近30天的记录
      const recentDates = viewedDates.slice(-30);
      await AsyncStorage.setItem(STORAGE_KEYS.DAILY_INSPIRATION_VIEWED, JSON.stringify(recentDates));
    }
  } catch (error) {
    console.error('标记每日灵感已查看失败:', error);
  }
};

export const getDailyInspirationViewedDates = async (): Promise<string[]> => {
  try {
    const viewedJson = await AsyncStorage.getItem(STORAGE_KEYS.DAILY_INSPIRATION_VIEWED);
    return viewedJson ? JSON.parse(viewedJson) : [];
  } catch (error) {
    console.error('获取每日灵感查看记录失败:', error);
    return [];
  }
};

// 清空所有数据
export const clearAllData = async (): Promise<void> => {
  try {
    await AsyncStorage.multiRemove(Object.values(STORAGE_KEYS));
  } catch (error) {
    console.error('清空所有数据失败:', error);
  }
};
