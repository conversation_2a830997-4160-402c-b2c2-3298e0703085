import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useTheme } from '../contexts/ThemeContext';
import { RootStackParamList } from '../types';
import { getFavoriteTools, addFavoriteTool, removeFavoriteTool } from '../utils/storage';

type ToolboxScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const { width } = Dimensions.get('window');

interface Tool {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'basic' | 'advanced';
  screen: keyof RootStackParamList;
  params?: any;
  color: string;
}

const tools: Tool[] = [
  {
    id: 'tarot',
    name: '塔罗牌占卜',
    description: '通过塔罗牌获得人生指导和洞察',
    icon: 'library',
    category: 'basic',
    screen: 'TarotReading',
    color: '#6B46C1',
  },
  {
    id: 'oracle',
    name: '神谕卡占卜',
    description: '接收来自天使和宇宙的信息',
    icon: 'sparkles',
    category: 'basic',
    screen: 'OracleReading',
    color: '#EC4899',
  },
  {
    id: 'oh',
    name: 'OH卡占卜',
    description: '通过图像和词语的组合获得洞察',
    icon: 'images',
    category: 'basic',
    screen: 'OHReading',
    color: '#F59E0B',
  },
  {
    id: 'crystal',
    name: '水晶疗愈',
    description: '利用水晶的能量平衡身心灵',
    icon: 'diamond',
    category: 'advanced',
    screen: 'CrystalHealing',
    color: '#10B981',
  },
  {
    id: 'meditation',
    name: '冥想引导',
    description: '通过冥想找到内在的平静',
    icon: 'leaf',
    category: 'advanced',
    screen: 'Meditation',
    color: '#8B5CF6',
  },
];

const ToolboxScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<ToolboxScreenNavigationProp>();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'basic' | 'advanced'>('all');
  const [favoriteTools, setFavoriteTools] = useState<string[]>([]);

  useEffect(() => {
    loadFavoriteTools();
  }, []);

  const loadFavoriteTools = async () => {
    try {
      const favorites = await getFavoriteTools();
      setFavoriteTools(favorites);
    } catch (error) {
      console.error('加载收藏工具失败:', error);
    }
  };

  const toggleFavorite = async (toolId: string) => {
    try {
      if (favoriteTools.includes(toolId)) {
        await removeFavoriteTool(toolId);
        setFavoriteTools(prev => prev.filter(id => id !== toolId));
      } else {
        await addFavoriteTool(toolId);
        setFavoriteTools(prev => [...prev, toolId]);
      }
    } catch (error) {
      console.error('切换收藏状态失败:', error);
    }
  };

  const filteredTools = tools.filter(tool => {
    const matchesSearch = tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || tool.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleToolPress = (tool: Tool) => {
    navigation.navigate(tool.screen, tool.params);
  };

  const renderSearchBar = () => (
    <View style={[styles.searchContainer, { backgroundColor: theme.colors.surface }]}>
      <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
      <TextInput
        style={[styles.searchInput, { color: theme.colors.text }]}
        placeholder="搜索占卜工具..."
        placeholderTextColor={theme.colors.textSecondary}
        value={searchQuery}
        onChangeText={setSearchQuery}
      />
      {searchQuery.length > 0 && (
        <TouchableOpacity onPress={() => setSearchQuery('')}>
          <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      )}
    </View>
  );

  const renderCategoryFilter = () => (
    <View style={styles.categoryFilter}>
      {[
        { key: 'all', label: '全部' },
        { key: 'basic', label: '基础工具' },
        { key: 'advanced', label: '高级工具' },
      ].map((category) => (
        <TouchableOpacity
          key={category.key}
          style={[
            styles.categoryButton,
            {
              backgroundColor: selectedCategory === category.key 
                ? theme.colors.primary 
                : theme.colors.surface,
            },
          ]}
          onPress={() => setSelectedCategory(category.key as any)}
        >
          <Text
            style={[
              styles.categoryButtonText,
              {
                color: selectedCategory === category.key 
                  ? '#FFFFFF' 
                  : theme.colors.text,
              },
            ]}
          >
            {category.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderToolCard = (tool: Tool) => (
    <TouchableOpacity
      key={tool.id}
      style={[styles.toolCard, { backgroundColor: theme.colors.surface }]}
      onPress={() => handleToolPress(tool)}
      activeOpacity={0.8}
    >
      <View style={styles.toolCardHeader}>
        <View style={[styles.toolIcon, { backgroundColor: tool.color + '20' }]}>
          <Ionicons name={tool.icon as any} size={28} color={tool.color} />
        </View>
        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={() => toggleFavorite(tool.id)}
        >
          <Ionicons
            name={favoriteTools.includes(tool.id) ? 'heart' : 'heart-outline'}
            size={20}
            color={favoriteTools.includes(tool.id) ? '#EF4444' : theme.colors.textSecondary}
          />
        </TouchableOpacity>
      </View>
      <Text style={[styles.toolName, { color: theme.colors.text }]}>
        {tool.name}
      </Text>
      <Text style={[styles.toolDescription, { color: theme.colors.textSecondary }]}>
        {tool.description}
      </Text>
    </TouchableOpacity>
  );

  const renderFavoriteTools = () => {
    const favoriteToolsData = tools.filter(tool => favoriteTools.includes(tool.id));
    
    if (favoriteToolsData.length === 0) return null;

    return (
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Ionicons name="heart" size={20} color={theme.colors.accent} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            我的收藏
          </Text>
        </View>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.favoriteToolsContainer}>
            {favoriteToolsData.map((tool) => (
              <TouchableOpacity
                key={tool.id}
                style={[styles.favoriteToolCard, { backgroundColor: theme.colors.surface }]}
                onPress={() => handleToolPress(tool)}
                activeOpacity={0.8}
              >
                <View style={[styles.favoriteToolIcon, { backgroundColor: tool.color + '20' }]}>
                  <Ionicons name={tool.icon as any} size={24} color={tool.color} />
                </View>
                <Text style={[styles.favoriteToolName, { color: theme.colors.text }]}>
                  {tool.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>
    );
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      showsVerticalScrollIndicator={false}
    >
      {renderSearchBar()}
      {renderCategoryFilter()}
      {renderFavoriteTools()}
      
      <View style={styles.toolsGrid}>
        {filteredTools.map(renderToolCard)}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
  },
  categoryFilter: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  favoriteToolsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
  },
  favoriteToolCard: {
    alignItems: 'center',
    padding: 16,
    marginRight: 12,
    borderRadius: 12,
    width: 100,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  favoriteToolIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  favoriteToolName: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  toolsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 8,
  },
  toolCard: {
    width: (width - 48) / 2,
    margin: 8,
    padding: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  toolCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  toolIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteButton: {
    padding: 4,
  },
  toolName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  toolDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default ToolboxScreen;
