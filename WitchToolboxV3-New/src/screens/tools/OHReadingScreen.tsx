import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';

const OHReadingScreen: React.FC = () => {
  const { theme } = useTheme();
  const [result, setResult] = useState<any>(null);

  const handleStartReading = () => {
    // 模拟OH卡占卜
    const mockResult = {
      imageCard: '桥梁',
      wordCard: '希望',
      interpretation: 'OH卡为你带来了"桥梁"与"希望"的组合。这个组合暗示着你正在经历一个重要的过渡期，需要在不同的选择或状态之间建立连接。希望的能量提醒你要保持乐观的态度。'
    };
    setResult(mockResult);
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.title, { color: theme.colors.text }]}>OH卡占卜</Text>
      
      {!result ? (
        <TouchableOpacity
          style={[styles.startButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleStartReading}
        >
          <Ionicons name="images" size={24} color="#FFFFFF" />
          <Text style={styles.startButtonText}>开始占卜</Text>
        </TouchableOpacity>
      ) : (
        <View style={styles.resultContainer}>
          <View style={[styles.cardContainer, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.cardLabel, { color: theme.colors.textSecondary }]}>图像卡</Text>
            <Text style={[styles.cardText, { color: theme.colors.text }]}>{result.imageCard}</Text>
          </View>
          
          <View style={[styles.cardContainer, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.cardLabel, { color: theme.colors.textSecondary }]}>词语卡</Text>
            <Text style={[styles.cardText, { color: theme.colors.text }]}>{result.wordCard}</Text>
          </View>
          
          <View style={[styles.interpretationContainer, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.interpretationTitle, { color: theme.colors.text }]}>解释</Text>
            <Text style={[styles.interpretationText, { color: theme.colors.textSecondary }]}>
              {result.interpretation}
            </Text>
          </View>
          
          <TouchableOpacity
            style={[styles.newReadingButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => setResult(null)}
          >
            <Text style={styles.newReadingButtonText}>重新占卜</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 32,
  },
  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 16,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  resultContainer: {
    alignItems: 'center',
  },
  cardContainer: {
    width: '100%',
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    alignItems: 'center',
  },
  cardLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  cardText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  interpretationContainer: {
    width: '100%',
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
  },
  interpretationTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  interpretationText: {
    fontSize: 16,
    lineHeight: 24,
  },
  newReadingButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 12,
  },
  newReadingButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default OHReadingScreen;
