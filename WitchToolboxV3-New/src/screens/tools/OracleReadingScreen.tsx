import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';

const OracleReadingScreen: React.FC = () => {
  const { theme } = useTheme();
  const [question, setQuestion] = useState('');
  const [cardCount, setCardCount] = useState(1);
  const [result, setResult] = useState<any>(null);
  const [isReading, setIsReading] = useState(false);

  const handleStartReading = async () => {
    setIsReading(true);
    
    // 模拟抽牌过程
    setTimeout(() => {
      const mockResult = {
        id: `oracle_${Date.now()}`,
        type: 'oracle',
        question,
        interpretation: '天使们正在为你带来爱与光明的信息。这张神谕卡提醒你要相信内在的智慧，保持开放的心态去接受宇宙的指引。',
        advice: '相信你的直觉，它会指引你走向正确的道路。保持感恩的心，让爱的能量流入你的生活。',
        timestamp: new Date()
      };
      setResult(mockResult);
      setIsReading(false);
    }, 2000);
  };

  const handleNewReading = () => {
    setResult(null);
    setQuestion('');
  };

  if (isReading) {
    return (
      <View style={[styles.container, styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <Ionicons name="sparkles" size={64} color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>
          正在连接天使能量...
        </Text>
        <Text style={[styles.loadingSubtext, { color: theme.colors.textSecondary }]}>
          请保持开放的心态，接收来自宇宙的信息
        </Text>
      </View>
    );
  }

  if (result) {
    return (
      <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.resultContainer}>
          <Text style={[styles.resultTitle, { color: theme.colors.text }]}>
            神谕卡指引
          </Text>
          
          {result.question && (
            <View style={[styles.questionCard, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.questionLabel, { color: theme.colors.textSecondary }]}>
                你的问题：
              </Text>
              <Text style={[styles.questionText, { color: theme.colors.text }]}>
                {result.question}
              </Text>
            </View>
          )}

          <View style={[styles.interpretationCard, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.interpretationTitle, { color: theme.colors.text }]}>
              天使的信息
            </Text>
            <Text style={[styles.interpretationText, { color: theme.colors.textSecondary }]}>
              {result.interpretation}
            </Text>
          </View>

          <View style={[styles.adviceCard, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.adviceTitle, { color: theme.colors.text }]}>
              指引与建议
            </Text>
            <Text style={[styles.adviceText, { color: theme.colors.textSecondary }]}>
              {result.advice}
            </Text>
          </View>

          <TouchableOpacity
            style={[styles.newReadingButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleNewReading}
          >
            <Ionicons name="refresh" size={20} color="#FFFFFF" />
            <Text style={styles.newReadingButtonText}>重新占卜</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          神谕卡占卜
        </Text>
        
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            选择抽牌数量
          </Text>
          <View style={styles.cardCountContainer}>
            {[1, 2, 3, 5].map((count) => (
              <TouchableOpacity
                key={count}
                style={[
                  styles.cardCountButton,
                  {
                    backgroundColor: cardCount === count 
                      ? theme.colors.primary 
                      : theme.colors.surface,
                  },
                ]}
                onPress={() => setCardCount(count)}
              >
                <Text
                  style={[
                    styles.cardCountText,
                    {
                      color: cardCount === count 
                        ? '#FFFFFF' 
                        : theme.colors.text,
                    },
                  ]}
                >
                  {count}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            你的问题（可选）
          </Text>
          <TextInput
            style={[
              styles.questionInput,
              {
                backgroundColor: theme.colors.surface,
                color: theme.colors.text,
                borderColor: theme.colors.primary + '30',
              },
            ]}
            placeholder="请输入你想要询问的问题..."
            placeholderTextColor={theme.colors.textSecondary}
            value={question}
            onChangeText={setQuestion}
            multiline
          />
        </View>

        <TouchableOpacity
          style={[styles.startButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleStartReading}
          disabled={isReading}
        >
          <Ionicons name="sparkles" size={24} color="#FFFFFF" />
          <Text style={styles.startButtonText}>
            {isReading ? '正在抽牌...' : '开始占卜'}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  loadingSubtext: {
    fontSize: 16,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  cardCountContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  cardCountButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardCountText: {
    fontSize: 18,
    fontWeight: '600',
  },
  questionInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 16,
    marginBottom: 24,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  resultContainer: {
    padding: 16,
  },
  resultTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
  },
  questionCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  questionLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  questionText: {
    fontSize: 16,
    lineHeight: 24,
  },
  interpretationCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  interpretationTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  interpretationText: {
    fontSize: 16,
    lineHeight: 24,
  },
  adviceCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  adviceTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  adviceText: {
    fontSize: 16,
    lineHeight: 24,
  },
  newReadingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
  },
  newReadingButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default OracleReadingScreen;
