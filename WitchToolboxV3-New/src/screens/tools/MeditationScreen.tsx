import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';

const MeditationScreen: React.FC = () => {
  const { theme } = useTheme();
  const [isPlaying, setIsPlaying] = useState(false);
  const [selectedDuration, setSelectedDuration] = useState(5);

  const durations = [5, 10, 15, 20, 30];

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.title, { color: theme.colors.text }]}>冥想引导</Text>
      
      <View style={styles.durationContainer}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>选择时长</Text>
        <View style={styles.durationButtons}>
          {durations.map((duration) => (
            <TouchableOpacity
              key={duration}
              style={[
                styles.durationButton,
                {
                  backgroundColor: selectedDuration === duration 
                    ? theme.colors.primary 
                    : theme.colors.surface,
                },
              ]}
              onPress={() => setSelectedDuration(duration)}
            >
              <Text
                style={[
                  styles.durationText,
                  {
                    color: selectedDuration === duration 
                      ? '#FFFFFF' 
                      : theme.colors.text,
                  },
                ]}
              >
                {duration}分钟
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      <View style={styles.playerContainer}>
        <View style={[styles.playerCircle, { backgroundColor: theme.colors.surface }]}>
          <TouchableOpacity
            style={[styles.playButton, { backgroundColor: theme.colors.primary }]}
            onPress={handlePlayPause}
          >
            <Ionicons 
              name={isPlaying ? 'pause' : 'play'} 
              size={32} 
              color="#FFFFFF" 
            />
          </TouchableOpacity>
        </View>
        
        <Text style={[styles.statusText, { color: theme.colors.textSecondary }]}>
          {isPlaying ? '正在冥想...' : '准备开始冥想'}
        </Text>
      </View>
      
      <View style={[styles.instructionCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.instructionTitle, { color: theme.colors.text }]}>
          冥想指导
        </Text>
        <Text style={[styles.instructionText, { color: theme.colors.textSecondary }]}>
          找一个安静舒适的地方坐下，闭上眼睛，专注于你的呼吸。让思绪自然流淌，不要强迫控制，只是观察它们的来去。
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 32,
  },
  durationContainer: {
    marginBottom: 48,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 16,
  },
  durationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
  },
  durationButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    margin: 4,
  },
  durationText: {
    fontSize: 14,
    fontWeight: '500',
  },
  playerContainer: {
    alignItems: 'center',
    marginBottom: 48,
  },
  playerCircle: {
    width: 200,
    height: 200,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  playButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    textAlign: 'center',
  },
  instructionCard: {
    padding: 20,
    borderRadius: 16,
  },
  instructionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    textAlign: 'center',
  },
  instructionText: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
});

export default MeditationScreen;
