import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';

const CrystalHealingScreen: React.FC = () => {
  const { theme } = useTheme();
  const [selectedCrystal, setSelectedCrystal] = useState<any>(null);

  const crystals = [
    {
      id: 'amethyst',
      name: '紫水晶',
      color: '#9333EA',
      properties: '增强直觉、促进冥想、提供精神保护',
      chakra: '顶轮、眉心轮',
      affirmation: '我与宇宙的智慧连接，内心充满平静与洞察力。'
    },
    {
      id: 'rose_quartz',
      name: '玫瑰石英',
      color: '#EC4899',
      properties: '促进自爱、治愈情感创伤、增强同情心',
      chakra: '心轮',
      affirmation: '我值得被爱，我给予和接受无条件的爱。'
    },
    {
      id: 'clear_quartz',
      name: '透明水晶',
      color: '#F3F4F6',
      properties: '能量放大、净化环境、增强专注力',
      chakra: '所有脉轮',
      affirmation: '我与宇宙的纯净能量连接，我的意识清晰明亮。'
    }
  ];

  const handleCrystalSelect = () => {
    const randomCrystal = crystals[Math.floor(Math.random() * crystals.length)];
    setSelectedCrystal(randomCrystal);
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.title, { color: theme.colors.text }]}>水晶疗愈</Text>
      
      {!selectedCrystal ? (
        <View style={styles.selectionContainer}>
          <Text style={[styles.instruction, { color: theme.colors.textSecondary }]}>
            让宇宙为你选择今日的疗愈水晶
          </Text>
          
          <TouchableOpacity
            style={[styles.selectButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleCrystalSelect}
          >
            <Ionicons name="diamond" size={24} color="#FFFFFF" />
            <Text style={styles.selectButtonText}>选择水晶</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.resultContainer}>
          <View style={[styles.crystalCard, { backgroundColor: theme.colors.surface }]}>
            <View style={[styles.crystalIcon, { backgroundColor: selectedCrystal.color + '20' }]}>
              <Ionicons name="diamond" size={48} color={selectedCrystal.color} />
            </View>
            <Text style={[styles.crystalName, { color: theme.colors.text }]}>
              {selectedCrystal.name}
            </Text>
          </View>
          
          <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.infoTitle, { color: theme.colors.text }]}>疗愈属性</Text>
            <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
              {selectedCrystal.properties}
            </Text>
          </View>
          
          <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.infoTitle, { color: theme.colors.text }]}>对应脉轮</Text>
            <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
              {selectedCrystal.chakra}
            </Text>
          </View>
          
          <View style={[styles.affirmationCard, { backgroundColor: theme.colors.primary + '20' }]}>
            <Text style={[styles.affirmationTitle, { color: theme.colors.primary }]}>
              今日肯定语
            </Text>
            <Text style={[styles.affirmationText, { color: theme.colors.text }]}>
              {selectedCrystal.affirmation}
            </Text>
          </View>
          
          <TouchableOpacity
            style={[styles.newSelectionButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => setSelectedCrystal(null)}
          >
            <Text style={styles.newSelectionButtonText}>重新选择</Text>
          </TouchableOpacity>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 32,
  },
  selectionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  instruction: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    paddingHorizontal: 32,
  },
  selectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 16,
  },
  selectButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  resultContainer: {
    alignItems: 'center',
  },
  crystalCard: {
    width: '100%',
    alignItems: 'center',
    padding: 24,
    borderRadius: 16,
    marginBottom: 16,
  },
  crystalIcon: {
    width: 96,
    height: 96,
    borderRadius: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  crystalName: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  infoCard: {
    width: '100%',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
  },
  affirmationCard: {
    width: '100%',
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
    alignItems: 'center',
  },
  affirmationTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  affirmationText: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  newSelectionButton: {
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 12,
  },
  newSelectionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CrystalHealingScreen;
