import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';
import { TarotCard, DivinationResult } from '../../types';
import { generateTarotReading, validateDivinationQuestion } from '../../utils/cardUtils';
import { saveDivinationResult } from '../../utils/storage';

const { width } = Dimensions.get('window');

// 模拟塔罗牌数据
const mockTarotCards: TarotCard[] = [
  {
    id: 'major_0',
    name: '愚者',
    englishName: 'The Fool',
    type: 'major',
    number: 0,
    image: 'tarot/major/fool.jpg',
    meanings: {
      upright: ['新的开始', '冒险精神', '纯真无邪', '自发性', '信任直觉'],
      reversed: ['鲁莽行事', '缺乏计划', '愚蠢决定', '过于天真', '不负责任']
    },
    keywords: ['开始', '旅程', '潜力', '信任', '冒险'],
    description: '愚者代表着人生旅程的开始，象征着纯真、自发性和对未知的信任。',
    element: '风',
    astrology: '天王星'
  },
  {
    id: 'major_1',
    name: '魔术师',
    englishName: 'The Magician',
    type: 'major',
    number: 1,
    image: 'tarot/major/magician.jpg',
    meanings: {
      upright: ['意志力', '创造力', '技能掌握', '专注力', '资源整合'],
      reversed: ['缺乏专注', '滥用权力', '操控他人', '缺乏技能', '浪费天赋']
    },
    keywords: ['意志', '技能', '创造', '专注', '实现'],
    description: '魔术师象征着将想法转化为现实的能力。',
    element: '风',
    astrology: '水星'
  },
  {
    id: 'major_2',
    name: '女祭司',
    englishName: 'The High Priestess',
    type: 'major',
    number: 2,
    image: 'tarot/major/high_priestess.jpg',
    meanings: {
      upright: ['直觉智慧', '内在知识', '神秘力量', '潜意识', '精神洞察'],
      reversed: ['忽视直觉', '缺乏洞察', '表面思考', '秘密暴露', '内在混乱']
    },
    keywords: ['直觉', '智慧', '神秘', '内在', '洞察'],
    description: '女祭司代表着内在的智慧和直觉力量。',
    element: '水',
    astrology: '月亮'
  }
];

// 塔罗牌阵
const tarotSpreads = [
  {
    id: 'single_card',
    name: '单张牌',
    description: '最简单的占卜方式，适合日常指导',
    cardCount: 1,
  },
  {
    id: 'three_card',
    name: '三张牌',
    description: '经典的过去-现在-未来牌阵',
    cardCount: 3,
  },
  {
    id: 'celtic_cross',
    name: '凯尔特十字',
    description: '最经典的复杂牌阵，提供全面的洞察',
    cardCount: 10,
  }
];

const TarotReadingScreen: React.FC = () => {
  const { theme } = useTheme();
  
  const [selectedSpread, setSelectedSpread] = useState('single_card');
  const [question, setQuestion] = useState('');
  const [isReading, setIsReading] = useState(false);
  const [readingResult, setReadingResult] = useState<DivinationResult | null>(null);

  const handleStartReading = async () => {
    // 验证问题
    const validation = validateDivinationQuestion(question);
    if (!validation.isValid) {
      Alert.alert('问题验证失败', validation.message);
      return;
    }

    setIsReading(true);

    // 模拟抽牌过程
    setTimeout(() => {
      const spread = tarotSpreads.find(s => s.id === selectedSpread);
      if (spread) {
        // 随机选择塔罗牌
        const shuffled = [...mockTarotCards].sort(() => Math.random() - 0.5);
        const drawnCards = shuffled.slice(0, spread.cardCount);
        
        // 生成占卜结果
        const result = generateTarotReading(drawnCards, question, selectedSpread);
        setReadingResult(result);
        
        // 保存结果
        saveDivinationResult(result);
      }
      setIsReading(false);
    }, 3000);
  };

  const handleNewReading = () => {
    setReadingResult(null);
    setQuestion('');
  };

  const renderSpreadSelection = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        选择牌阵
      </Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.spreadContainer}>
          {tarotSpreads.map((spread) => (
            <TouchableOpacity
              key={spread.id}
              style={[
                styles.spreadCard,
                {
                  backgroundColor: selectedSpread === spread.id 
                    ? theme.colors.primary + '20' 
                    : theme.colors.surface,
                  borderColor: selectedSpread === spread.id 
                    ? theme.colors.primary 
                    : 'transparent',
                },
              ]}
              onPress={() => setSelectedSpread(spread.id)}
            >
              <Text style={[styles.spreadName, { color: theme.colors.text }]}>
                {spread.name}
              </Text>
              <Text style={[styles.spreadDescription, { color: theme.colors.textSecondary }]}>
                {spread.description}
              </Text>
              <View style={styles.spreadInfo}>
                <Ionicons name="library" size={16} color={theme.colors.textSecondary} />
                <Text style={[styles.spreadCardCount, { color: theme.colors.textSecondary }]}>
                  {spread.cardCount} 张牌
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );

  const renderQuestionInput = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        你的问题（可选）
      </Text>
      <TextInput
        style={[
          styles.questionInput,
          {
            backgroundColor: theme.colors.surface,
            color: theme.colors.text,
            borderColor: theme.colors.primary + '30',
          },
        ]}
        placeholder="请输入你想要询问的问题..."
        placeholderTextColor={theme.colors.textSecondary}
        value={question}
        onChangeText={setQuestion}
        multiline
        maxLength={200}
      />
      <Text style={[styles.questionHint, { color: theme.colors.textSecondary }]}>
        专注于你真正关心的问题，塔罗牌会为你提供指导
      </Text>
    </View>
  );

  const renderResult = () => {
    if (!readingResult) return null;

    return (
      <View style={styles.resultContainer}>
        <Text style={[styles.resultTitle, { color: theme.colors.text }]}>
          占卜结果
        </Text>
        
        {readingResult.question && (
          <View style={[styles.questionCard, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.questionLabel, { color: theme.colors.textSecondary }]}>
              你的问题：
            </Text>
            <Text style={[styles.questionText, { color: theme.colors.text }]}>
              {readingResult.question}
            </Text>
          </View>
        )}

        <View style={[styles.interpretationCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.interpretationTitle, { color: theme.colors.text }]}>
            解释
          </Text>
          <Text style={[styles.interpretationText, { color: theme.colors.textSecondary }]}>
            {readingResult.interpretation}
          </Text>
        </View>

        <View style={[styles.adviceCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.adviceTitle, { color: theme.colors.text }]}>
            建议
          </Text>
          <Text style={[styles.adviceText, { color: theme.colors.textSecondary }]}>
            {readingResult.advice}
          </Text>
        </View>

        <TouchableOpacity
          style={[styles.newReadingButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleNewReading}
        >
          <Ionicons name="refresh" size={20} color="#FFFFFF" />
          <Text style={styles.newReadingButtonText}>重新占卜</Text>
        </TouchableOpacity>
      </View>
    );
  };

  if (isReading) {
    return (
      <View style={[styles.container, styles.loadingContainer, { backgroundColor: theme.colors.background }]}>
        <Ionicons name="sparkles" size={64} color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>
          正在为你占卜...
        </Text>
        <Text style={[styles.loadingSubtext, { color: theme.colors.textSecondary }]}>
          请保持专注，让宇宙的能量指引你
        </Text>
      </View>
    );
  }

  if (readingResult) {
    return (
      <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {renderResult()}
      </ScrollView>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      showsVerticalScrollIndicator={false}
    >
      {renderSpreadSelection()}
      {renderQuestionInput()}
      
      <TouchableOpacity
        style={[styles.startButton, { backgroundColor: theme.colors.primary }]}
        onPress={handleStartReading}
        disabled={isReading}
      >
        <Ionicons name="play" size={24} color="#FFFFFF" />
        <Text style={styles.startButtonText}>开始占卜</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  loadingSubtext: {
    fontSize: 16,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  section: {
    margin: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  spreadContainer: {
    flexDirection: 'row',
    paddingHorizontal: 4,
  },
  spreadCard: {
    width: width * 0.7,
    padding: 16,
    borderRadius: 16,
    marginRight: 12,
    borderWidth: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  spreadName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  spreadDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  spreadInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  spreadCardCount: {
    fontSize: 12,
    marginLeft: 4,
  },
  questionInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  questionHint: {
    fontSize: 12,
    marginTop: 8,
    fontStyle: 'italic',
  },
  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 16,
    padding: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  resultContainer: {
    padding: 16,
  },
  resultTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
  },
  questionCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  questionLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  questionText: {
    fontSize: 16,
    lineHeight: 24,
  },
  interpretationCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  interpretationTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  interpretationText: {
    fontSize: 16,
    lineHeight: 24,
  },
  adviceCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  adviceTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  adviceText: {
    fontSize: 16,
    lineHeight: 24,
  },
  newReadingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
  },
  newReadingButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default TarotReadingScreen;
