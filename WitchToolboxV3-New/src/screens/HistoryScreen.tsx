import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { DivinationResult } from '../types';
import { getDivinationHistory, deleteDivinationResult } from '../utils/storage';

const HistoryScreen: React.FC = () => {
  const { theme } = useTheme();
  const [history, setHistory] = useState<DivinationResult[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadHistory();
  }, []);

  const loadHistory = async () => {
    try {
      const historyData = await getDivinationHistory();
      setHistory(historyData);
    } catch (error) {
      console.error('加载历史记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteResult = (resultId: string) => {
    Alert.alert(
      '删除确认',
      '确定要删除这条占卜记录吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteDivinationResult(resultId);
              setHistory(prev => prev.filter(item => item.id !== resultId));
            } catch (error) {
              console.error('删除记录失败:', error);
            }
          },
        },
      ]
    );
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'tarot':
        return 'library';
      case 'oracle':
        return 'sparkles';
      case 'oh':
        return 'images';
      case 'crystal':
        return 'diamond';
      default:
        return 'help-circle';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'tarot':
        return '塔罗牌';
      case 'oracle':
        return '神谕卡';
      case 'oh':
        return 'OH卡';
      case 'crystal':
        return '水晶';
      default:
        return '未知';
    }
  };

  const renderHistoryItem = (item: DivinationResult) => (
    <View key={item.id} style={[styles.historyItem, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.historyHeader}>
        <View style={styles.typeInfo}>
          <Ionicons 
            name={getTypeIcon(item.type) as any} 
            size={20} 
            color={theme.colors.primary} 
          />
          <Text style={[styles.typeText, { color: theme.colors.primary }]}>
            {getTypeLabel(item.type)}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteResult(item.id)}
        >
          <Ionicons name="trash" size={18} color={theme.colors.error} />
        </TouchableOpacity>
      </View>
      
      <Text style={[styles.timestamp, { color: theme.colors.textSecondary }]}>
        {item.timestamp.toLocaleDateString('zh-CN')} {item.timestamp.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}
      </Text>
      
      {item.question && (
        <Text style={[styles.question, { color: theme.colors.text }]}>
          问题：{item.question}
        </Text>
      )}
      
      <Text style={[styles.interpretation, { color: theme.colors.textSecondary }]} numberOfLines={3}>
        {item.interpretation}
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContainer, { backgroundColor: theme.colors.background }]}>
        <Ionicons name="hourglass" size={48} color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>
          加载历史记录...
        </Text>
      </View>
    );
  }

  if (history.length === 0) {
    return (
      <View style={[styles.container, styles.centerContainer, { backgroundColor: theme.colors.background }]}>
        <Ionicons name="time" size={64} color={theme.colors.textSecondary} />
        <Text style={[styles.emptyText, { color: theme.colors.text }]}>
          暂无占卜记录
        </Text>
        <Text style={[styles.emptySubtext, { color: theme.colors.textSecondary }]}>
          开始你的第一次占卜吧
        </Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      showsVerticalScrollIndicator={false}
    >
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          占卜历史
        </Text>
        <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
          共 {history.length} 条记录
        </Text>
      </View>
      
      <View style={styles.historyList}>
        {history.map(renderHistoryItem)}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
  },
  header: {
    padding: 16,
    paddingTop: 8,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
  },
  historyList: {
    padding: 16,
  },
  historyItem: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  typeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  deleteButton: {
    padding: 4,
  },
  timestamp: {
    fontSize: 12,
    marginBottom: 8,
  },
  question: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  interpretation: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default HistoryScreen;
