import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { UserPreferences } from '../types';
import { getUserPreferences, saveUserPreferences, clearAllData } from '../utils/storage';

const SettingsScreen: React.FC = () => {
  const { theme, isDark, toggleTheme } = useTheme();
  const [preferences, setPreferences] = useState<UserPreferences>({
    theme: 'dark',
    language: 'zh',
    notifications: true,
    soundEnabled: true,
    favoriteTools: []
  });

  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = async () => {
    try {
      const userPrefs = await getUserPreferences();
      setPreferences(userPrefs);
    } catch (error) {
      console.error('加载用户偏好失败:', error);
    }
  };

  const updatePreference = async (key: keyof UserPreferences, value: any) => {
    try {
      const updatedPrefs = { ...preferences, [key]: value };
      setPreferences(updatedPrefs);
      await saveUserPreferences(updatedPrefs);
    } catch (error) {
      console.error('保存用户偏好失败:', error);
    }
  };

  const handleClearData = () => {
    Alert.alert(
      '清除数据',
      '确定要清除所有应用数据吗？这将删除所有占卜记录、学习进度和设置。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确定',
          style: 'destructive',
          onPress: async () => {
            try {
              await clearAllData();
              Alert.alert('成功', '所有数据已清除');
            } catch (error) {
              console.error('清除数据失败:', error);
              Alert.alert('错误', '清除数据失败');
            }
          },
        },
      ]
    );
  };

  const renderSettingItem = (
    icon: string,
    title: string,
    subtitle?: string,
    rightComponent?: React.ReactNode,
    onPress?: () => void
  ) => (
    <TouchableOpacity
      style={[styles.settingItem, { backgroundColor: theme.colors.surface }]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingLeft}>
        <View style={[styles.settingIcon, { backgroundColor: theme.colors.primary + '20' }]}>
          <Ionicons name={icon as any} size={20} color={theme.colors.primary} />
        </View>
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, { color: theme.colors.text }]}>
            {title}
          </Text>
          {subtitle && (
            <Text style={[styles.settingSubtitle, { color: theme.colors.textSecondary }]}>
              {subtitle}
            </Text>
          )}
        </View>
      </View>
      {rightComponent || (
        onPress && <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
      )}
    </TouchableOpacity>
  );

  const renderSection = (title: string, children: React.ReactNode) => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        {title}
      </Text>
      {children}
    </View>
  );

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      showsVerticalScrollIndicator={false}
    >
      {renderSection('外观', (
        <>
          {renderSettingItem(
            'moon',
            '深色模式',
            isDark ? '已开启' : '已关闭',
            <Switch
              value={isDark}
              onValueChange={toggleTheme}
              trackColor={{ false: theme.colors.textSecondary, true: theme.colors.primary }}
              thumbColor="#FFFFFF"
            />
          )}
        </>
      ))}

      {renderSection('通知', (
        <>
          {renderSettingItem(
            'notifications',
            '推送通知',
            preferences.notifications ? '已开启' : '已关闭',
            <Switch
              value={preferences.notifications}
              onValueChange={(value) => updatePreference('notifications', value)}
              trackColor={{ false: theme.colors.textSecondary, true: theme.colors.primary }}
              thumbColor="#FFFFFF"
            />
          )}
          {renderSettingItem(
            'volume-high',
            '声音效果',
            preferences.soundEnabled ? '已开启' : '已关闭',
            <Switch
              value={preferences.soundEnabled}
              onValueChange={(value) => updatePreference('soundEnabled', value)}
              trackColor={{ false: theme.colors.textSecondary, true: theme.colors.primary }}
              thumbColor="#FFFFFF"
            />
          )}
        </>
      ))}

      {renderSection('数据', (
        <>
          {renderSettingItem(
            'download',
            '导出数据',
            '导出所有占卜记录和设置',
            undefined,
            () => Alert.alert('提示', '导出功能开发中')
          )}
          {renderSettingItem(
            'trash',
            '清除所有数据',
            '删除所有占卜记录、学习进度和设置',
            undefined,
            handleClearData
          )}
        </>
      ))}

      {renderSection('关于', (
        <>
          {renderSettingItem(
            'information-circle',
            '版本信息',
            '女巫工具箱 V3.0.0'
          )}
          {renderSettingItem(
            'help-circle',
            '帮助与支持',
            '使用指南和常见问题',
            undefined,
            () => Alert.alert('提示', '帮助页面开发中')
          )}
          {renderSettingItem(
            'star',
            '评价应用',
            '在应用商店给我们评分',
            undefined,
            () => Alert.alert('提示', '感谢您的支持！')
          )}
        </>
      ))}

      <View style={styles.footer}>
        <Text style={[styles.footerText, { color: theme.colors.textSecondary }]}>
          女巫工具箱 V3 - 探索身心灵的奥秘
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    marginLeft: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 12,
  },
  footer: {
    padding: 32,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default SettingsScreen;
