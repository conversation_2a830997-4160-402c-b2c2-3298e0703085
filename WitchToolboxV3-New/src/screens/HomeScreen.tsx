import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useTheme } from '../contexts/ThemeContext';
import { RootStackParamList, DailyInspiration, RecommendedContent } from '../types';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const { width } = Dimensions.get('window');

const HomeScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation<HomeScreenNavigationProp>();
  
  const [refreshing, setRefreshing] = useState(false);
  const [bannerIndex, setBannerIndex] = useState(0);

  // Banner数据
  const banners = [
    {
      id: 1,
      title: '探索内在智慧',
      subtitle: '让塔罗牌指引你的人生道路',
      colors: ['#6B46C1', '#9333EA'],
      action: () => navigation.navigate('TarotReading', { spread: 'three_card' })
    },
    {
      id: 2,
      title: '天使的祝福',
      subtitle: '接收来自天使的爱与指引',
      colors: ['#EC4899', '#F97316'],
      action: () => navigation.navigate('OracleReading')
    },
    {
      id: 3,
      title: '水晶的力量',
      subtitle: '用水晶的能量治愈身心灵',
      colors: ['#10B981', '#059669'],
      action: () => navigation.navigate('CrystalHealing')
    }
  ];

  // 每日灵感数据
  const dailyInspiration: DailyInspiration = {
    id: 'daily_' + new Date().toISOString().split('T')[0],
    date: new Date().toISOString().split('T')[0],
    message: '今天是一个充满可能性的新开始，相信你内在的智慧会指引你走向正确的道路。',
    affirmation: '我值得拥有所有美好的事物，我接受宇宙的丰盛。'
  };

  // 推荐内容数据
  const recommendations: RecommendedContent[] = [
    {
      id: 'rec_1',
      title: '塔罗牌占卜',
      type: 'tool',
      image: 'tarot.jpg',
      description: '通过塔罗牌获得人生指导和洞察',
      targetScreen: 'TarotReading',
      params: { spread: 'three_card' }
    },
    {
      id: 'rec_2',
      title: '神谕卡占卜',
      type: 'tool',
      image: 'oracle.jpg',
      description: '接收来自天使的爱与指引',
      targetScreen: 'OracleReading'
    },
    {
      id: 'rec_3',
      title: '水晶疗愈',
      type: 'tool',
      image: 'crystal.jpg',
      description: '利用水晶的能量平衡身心灵',
      targetScreen: 'CrystalHealing'
    },
    {
      id: 'rec_4',
      title: '冥想引导',
      type: 'tool',
      image: 'meditation.jpg',
      description: '通过冥想找到内在的平静',
      targetScreen: 'Meditation'
    }
  ];

  useEffect(() => {
    // Banner自动轮播
    const bannerTimer = setInterval(() => {
      setBannerIndex((prev) => (prev + 1) % banners.length);
    }, 5000);

    return () => clearInterval(bannerTimer);
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    // 模拟刷新
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleRecommendationPress = (rec: RecommendedContent) => {
    navigation.navigate(rec.targetScreen as any, rec.params);
  };

  const renderBanner = () => {
    const currentBanner = banners[bannerIndex];
    
    return (
      <TouchableOpacity
        style={styles.bannerContainer}
        onPress={currentBanner.action}
        activeOpacity={0.9}
      >
        <LinearGradient
          colors={currentBanner.colors}
          style={styles.banner}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.bannerContent}>
            <Text style={[styles.bannerTitle, { color: '#FFFFFF' }]}>
              {currentBanner.title}
            </Text>
            <Text style={[styles.bannerSubtitle, { color: '#FFFFFF' }]}>
              {currentBanner.subtitle}
            </Text>
          </View>
          <View style={styles.bannerIndicators}>
            {banners.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.indicator,
                  {
                    backgroundColor: index === bannerIndex ? '#FFFFFF' : '#FFFFFF50',
                  },
                ]}
              />
            ))}
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  const renderDailyInspiration = () => (
    <View style={[styles.inspirationCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.inspirationHeader}>
        <Ionicons name="sparkles" size={24} color={theme.colors.accent} />
        <Text style={[styles.inspirationTitle, { color: theme.colors.text }]}>
          今日灵感
        </Text>
      </View>
      <Text style={[styles.inspirationMessage, { color: theme.colors.textSecondary }]}>
        {dailyInspiration.message}
      </Text>
      <Text style={[styles.inspirationAffirmation, { color: theme.colors.primary }]}>
        💫 {dailyInspiration.affirmation}
      </Text>
    </View>
  );

  const renderRecommendations = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        推荐内容
      </Text>
      <View style={styles.recommendationsGrid}>
        {recommendations.map((rec) => (
          <TouchableOpacity
            key={rec.id}
            style={[styles.recommendationCard, { backgroundColor: theme.colors.surface }]}
            onPress={() => handleRecommendationPress(rec)}
            activeOpacity={0.8}
          >
            <View style={[styles.recommendationIcon, { backgroundColor: theme.colors.primary + '20' }]}>
              <Ionicons 
                name={getRecommendationIcon(rec.type)} 
                size={24} 
                color={theme.colors.primary} 
              />
            </View>
            <Text style={[styles.recommendationTitle, { color: theme.colors.text }]}>
              {rec.title}
            </Text>
            <Text style={[styles.recommendationDescription, { color: theme.colors.textSecondary }]}>
              {rec.description}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderQuickActions = () => {
    const quickActions = [
      { icon: 'library', title: '工具箱', action: () => navigation.navigate('Main', { screen: 'Toolbox' }) },
      { icon: 'school', title: '学习', action: () => navigation.navigate('Main', { screen: 'Learning' }) },
      { icon: 'fitness', title: '实践', action: () => navigation.navigate('Main', { screen: 'Practice' }) },
      { icon: 'time', title: '历史', action: () => navigation.navigate('History') },
    ];

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          快速入口
        </Text>
        <View style={styles.quickActionsContainer}>
          {quickActions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.quickActionButton, { backgroundColor: theme.colors.surface }]}
              onPress={action.action}
              activeOpacity={0.8}
            >
              <Ionicons name={action.icon as any} size={28} color={theme.colors.primary} />
              <Text style={[styles.quickActionText, { color: theme.colors.text }]}>
                {action.title}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'tool':
        return 'library';
      case 'learning':
        return 'school';
      case 'practice':
        return 'fitness';
      default:
        return 'help-circle';
    }
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          tintColor={theme.colors.primary}
          colors={[theme.colors.primary]}
        />
      }
      showsVerticalScrollIndicator={false}
    >
      {renderBanner()}
      {renderDailyInspiration()}
      {renderRecommendations()}
      {renderQuickActions()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  bannerContainer: {
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  banner: {
    height: 180,
    justifyContent: 'space-between',
    padding: 20,
  },
  bannerContent: {
    flex: 1,
    justifyContent: 'center',
  },
  bannerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  bannerSubtitle: {
    fontSize: 16,
    opacity: 0.9,
  },
  bannerIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  inspirationCard: {
    margin: 16,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  inspirationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  inspirationTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  inspirationMessage: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 12,
  },
  inspirationAffirmation: {
    fontSize: 14,
    fontWeight: '500',
    fontStyle: 'italic',
  },
  section: {
    margin: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  recommendationsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  recommendationCard: {
    width: (width - 48) / 2,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  recommendationIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  recommendationTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  recommendationDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 4,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: '500',
    marginTop: 8,
  },
});

export default HomeScreen;
