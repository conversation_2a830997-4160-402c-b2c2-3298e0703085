import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';

const LearningScreen: React.FC = () => {
  const { theme } = useTheme();

  const learningCategories = [
    {
      id: 'tarot_basics',
      title: '塔罗牌基础',
      description: '从零开始学习塔罗牌占卜',
      icon: 'library',
      color: '#6B46C1',
      lessons: 12,
      duration: '2小时',
    },
    {
      id: 'crystal_healing',
      title: '水晶疗愈入门',
      description: '了解水晶的神奇疗愈力量',
      icon: 'diamond',
      color: '#10B981',
      lessons: 8,
      duration: '1.5小时',
    },
    {
      id: 'meditation',
      title: '冥想与正念',
      description: '学习冥想技巧，找到内在平静',
      icon: 'leaf',
      color: '#8B5CF6',
      lessons: 15,
      duration: '3小时',
    },
    {
      id: 'astrology',
      title: '占星学基础',
      description: '探索星座与命运的奥秘',
      icon: 'planet',
      color: '#F59E0B',
      lessons: 20,
      duration: '4小时',
    },
  ];

  const renderCategoryCard = (category: any) => (
    <TouchableOpacity
      key={category.id}
      style={[styles.categoryCard, { backgroundColor: theme.colors.surface }]}
      activeOpacity={0.8}
    >
      <View style={[styles.categoryIcon, { backgroundColor: category.color + '20' }]}>
        <Ionicons name={category.icon as any} size={32} color={category.color} />
      </View>
      <View style={styles.categoryContent}>
        <Text style={[styles.categoryTitle, { color: theme.colors.text }]}>
          {category.title}
        </Text>
        <Text style={[styles.categoryDescription, { color: theme.colors.textSecondary }]}>
          {category.description}
        </Text>
        <View style={styles.categoryStats}>
          <View style={styles.statItem}>
            <Ionicons name="book" size={16} color={theme.colors.textSecondary} />
            <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
              {category.lessons} 课程
            </Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons name="time" size={16} color={theme.colors.textSecondary} />
            <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
              {category.duration}
            </Text>
          </View>
        </View>
      </View>
      <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
    </TouchableOpacity>
  );

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      showsVerticalScrollIndicator={false}
    >
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          学习中心
        </Text>
        <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
          探索身心灵的奥秘，提升你的占卜技能
        </Text>
      </View>

      <View style={[styles.statsContainer, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: theme.colors.primary }]}>
            55
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
            总课程
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: theme.colors.success }]}>
            12
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
            已完成
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: theme.colors.warning }]}>
            3
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
            学习中
          </Text>
        </View>
      </View>

      <View style={styles.categoriesContainer}>
        {learningCategories.map(renderCategoryCard)}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingTop: 8,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    lineHeight: 24,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    margin: 16,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statItem: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  statText: {
    fontSize: 12,
    marginLeft: 4,
  },
  categoriesContainer: {
    padding: 16,
  },
  categoryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  categoryIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  categoryContent: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  categoryStats: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default LearningScreen;
