import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';

const PracticeScreen: React.FC = () => {
  const { theme } = useTheme();

  const practiceGuides = [
    {
      id: 'online_divination',
      title: '开设在线占卜服务',
      description: '学习如何将占卜技能转化为收入来源',
      category: '变现指导',
      icon: 'cash',
      color: '#10B981',
      steps: 6,
      difficulty: 'intermediate',
      estimatedTime: '2小时',
    },
    {
      id: 'social_media',
      title: '身心灵内容创作',
      description: '在社交媒体上分享你的占卜知识',
      category: '自媒体运营',
      icon: 'megaphone',
      color: '#EC4899',
      steps: 5,
      difficulty: 'beginner',
      estimatedTime: '1.5小时',
    },
    {
      id: 'daily_meditation',
      title: '每日冥想实践',
      description: '建立每日冥想的习惯',
      category: '实践指导',
      icon: 'leaf',
      color: '#8B5CF6',
      steps: 5,
      difficulty: 'beginner',
      estimatedTime: '30分钟',
    },
    {
      id: 'crystal_workshop',
      title: '水晶疗愈工作坊',
      description: '组织和运营水晶疗愈工作坊',
      category: '变现指导',
      icon: 'diamond',
      color: '#F59E0B',
      steps: 8,
      difficulty: 'advanced',
      estimatedTime: '3小时',
    },
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return '#10B981';
      case 'intermediate':
        return '#F59E0B';
      case 'advanced':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  const getDifficultyLabel = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return '初级';
      case 'intermediate':
        return '中级';
      case 'advanced':
        return '高级';
      default:
        return '未知';
    }
  };

  const renderGuideCard = (guide: any) => (
    <TouchableOpacity
      key={guide.id}
      style={[styles.guideCard, { backgroundColor: theme.colors.surface }]}
      activeOpacity={0.8}
    >
      <View style={styles.guideHeader}>
        <View style={[styles.guideIcon, { backgroundColor: guide.color + '20' }]}>
          <Ionicons name={guide.icon as any} size={28} color={guide.color} />
        </View>
        <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(guide.difficulty) + '20' }]}>
          <Text style={[styles.difficultyText, { color: getDifficultyColor(guide.difficulty) }]}>
            {getDifficultyLabel(guide.difficulty)}
          </Text>
        </View>
      </View>
      
      <Text style={[styles.guideTitle, { color: theme.colors.text }]}>
        {guide.title}
      </Text>
      
      <Text style={[styles.guideDescription, { color: theme.colors.textSecondary }]}>
        {guide.description}
      </Text>
      
      <Text style={[styles.guideCategory, { color: theme.colors.primary }]}>
        {guide.category}
      </Text>
      
      <View style={styles.guideStats}>
        <View style={styles.statItem}>
          <Ionicons name="list" size={16} color={theme.colors.textSecondary} />
          <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
            {guide.steps} 步骤
          </Text>
        </View>
        <View style={styles.statItem}>
          <Ionicons name="time" size={16} color={theme.colors.textSecondary} />
          <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
            {guide.estimatedTime}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      showsVerticalScrollIndicator={false}
    >
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          实践指导
        </Text>
        <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
          将你的身心灵知识转化为实际行动和收入
        </Text>
      </View>

      <View style={[styles.statsContainer, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: theme.colors.primary }]}>
            25
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
            总指导
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: theme.colors.success }]}>
            5
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
            已完成
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={[styles.statNumber, { color: theme.colors.warning }]}>
            2
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
            进行中
          </Text>
        </View>
      </View>

      <View style={styles.guidesContainer}>
        {practiceGuides.map(renderGuideCard)}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingTop: 8,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    lineHeight: 24,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    margin: 16,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statItem: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  statText: {
    fontSize: 12,
    marginLeft: 4,
  },
  guidesContainer: {
    padding: 16,
  },
  guideCard: {
    padding: 16,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  guideHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  guideIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  difficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  difficultyText: {
    fontSize: 12,
    fontWeight: '500',
  },
  guideTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  guideDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  guideCategory: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 12,
  },
  guideStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});

export default PracticeScreen;
