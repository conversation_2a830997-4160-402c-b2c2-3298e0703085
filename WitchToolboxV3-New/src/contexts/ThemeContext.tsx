import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Theme } from '../types';

// 深紫色神秘主题
const darkTheme: Theme = {
  colors: {
    primary: '#6B46C1', // 深紫色
    secondary: '#9333EA', // 紫色
    background: '#0F0B1A', // 深黑紫色
    surface: '#1A1625', // 深紫灰色
    text: '#F3F4F6', // 浅灰白色
    textSecondary: '#9CA3AF', // 中灰色
    accent: '#F59E0B', // 金色
    error: '#EF4444', // 红色
    success: '#10B981', // 绿色
    warning: '#F59E0B', // 橙色
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  borderRadius: {
    sm: 8,
    md: 12,
    lg: 16,
  },
  typography: {
    h1: {
      fontSize: 32,
      fontWeight: 'bold',
      color: '#F3F4F6',
      marginBottom: 16,
    },
    h2: {
      fontSize: 24,
      fontWeight: '600',
      color: '#F3F4F6',
      marginBottom: 12,
    },
    h3: {
      fontSize: 20,
      fontWeight: '600',
      color: '#F3F4F6',
      marginBottom: 8,
    },
    body: {
      fontSize: 16,
      fontWeight: 'normal',
      color: '#F3F4F6',
      lineHeight: 24,
    },
    caption: {
      fontSize: 14,
      fontWeight: 'normal',
      color: '#9CA3AF',
      lineHeight: 20,
    },
  },
};

const lightTheme: Theme = {
  colors: {
    primary: '#6B46C1',
    secondary: '#9333EA',
    background: '#F9FAFB',
    surface: '#FFFFFF',
    text: '#111827',
    textSecondary: '#6B7280',
    accent: '#F59E0B',
    error: '#EF4444',
    success: '#10B981',
    warning: '#F59E0B',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  borderRadius: {
    sm: 8,
    md: 12,
    lg: 16,
  },
  typography: {
    h1: {
      fontSize: 32,
      fontWeight: 'bold',
      color: '#111827',
      marginBottom: 16,
    },
    h2: {
      fontSize: 24,
      fontWeight: '600',
      color: '#111827',
      marginBottom: 12,
    },
    h3: {
      fontSize: 20,
      fontWeight: '600',
      color: '#111827',
      marginBottom: 8,
    },
    body: {
      fontSize: 16,
      fontWeight: 'normal',
      color: '#111827',
      lineHeight: 24,
    },
    caption: {
      fontSize: 14,
      fontWeight: 'normal',
      color: '#6B7280',
      lineHeight: 20,
    },
  },
};

interface ThemeContextType {
  theme: Theme;
  isDark: boolean;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [isDark, setIsDark] = useState(true); // 默认使用深色主题

  const toggleTheme = () => {
    setIsDark(!isDark);
  };

  const theme = isDark ? darkTheme : lightTheme;

  return (
    <ThemeContext.Provider value={{ theme, isDark, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
