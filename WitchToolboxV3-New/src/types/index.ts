// 基础类型定义

// 塔罗牌类型
export interface TarotCard {
  id: string;
  name: string;
  englishName: string;
  type: 'major' | 'minor';
  number: number;
  suit?: 'wands' | 'cups' | 'swords' | 'pentacles';
  image: string;
  meanings: {
    upright: string[];
    reversed: string[];
  };
  keywords: string[];
  description: string;
  element: string;
  astrology: string;
}

// 神谕卡类型
export interface OracleCard {
  id: string;
  name: string;
  deck: string;
  image: string;
  meaning: string;
  guidance: string;
  affirmation: string;
  keywords: string[];
}

// OH卡类型
export interface OHCard {
  id: string;
  type: 'image' | 'word';
  content: string;
  image?: string;
  category?: string;
}

// 水晶类型
export interface Crystal {
  id: string;
  name: string;
  englishName: string;
  color: string[];
  chakra: string[];
  element: string;
  hardness: number;
  origin: string[];
  image: string;
  description: string;
  metaphysicalProperties: string;
  healingProperties: string[];
  emotionalHealing: string[];
  spiritualHealing: string[];
  physicalHealing: string[];
  uses: string[];
  cleansing: string[];
  charging: string[];
  compatibility: string[];
  cautions: string[];
  affirmation: string;
}

// 占星学类型
export interface ZodiacSign {
  id: string;
  name: string;
  englishName: string;
  symbol: string;
  element: string;
  quality: string;
  rulingPlanet: string;
  dates: string;
  traits: string[];
  strengths: string[];
  weaknesses: string[];
  compatibility: string[];
  description: string;
}

// 易经卦象类型
export interface Hexagram {
  id: string;
  number: number;
  name: string;
  englishName: string;
  trigrams: {
    upper: string;
    lower: string;
  };
  meaning: string;
  judgment: string;
  image: string;
  lines: string[];
  interpretation: string;
}

// 占卜结果类型
export interface DivinationResult {
  id: string;
  type: 'tarot' | 'oracle' | 'oh' | 'numerology' | 'astrology' | 'crystal' | 'iching';
  question?: string;
  cards?: (TarotCard | OracleCard | OHCard)[];
  crystal?: Crystal;
  interpretation: string;
  advice: string;
  timestamp: Date;
  spread?: string;
}

// 学习内容类型
export interface LearningContent {
  id: string;
  title: string;
  category: string;
  type: 'video' | 'article' | 'quiz';
  duration?: number;
  content: string;
  videoUrl?: string;
  completed: boolean;
  progress: number;
  quiz?: Quiz;
}

// 测验类型
export interface Quiz {
  id: string;
  questions: QuizQuestion[];
  passingScore: number;
}

export interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
}

// 实践指导类型
export interface PracticeGuide {
  id: string;
  title: string;
  category: string;
  steps: PracticeStep[];
  estimatedTime: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  completed: boolean;
}

export interface PracticeStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  resources?: string[];
}

// 用户偏好类型
export interface UserPreferences {
  theme: 'light' | 'dark';
  language: 'zh' | 'en';
  notifications: boolean;
  soundEnabled: boolean;
  favoriteTools: string[];
}

// 导航类型
export type RootStackParamList = {
  Main: undefined;
  TarotReading: { spread?: string };
  OracleReading: undefined;
  OHReading: undefined;
  CrystalHealing: undefined;
  Meditation: undefined;
  LearningDetail: { contentId: string };
  PracticeDetail: { guideId: string };
  History: undefined;
  Settings: undefined;
};

export type TabParamList = {
  Home: undefined;
  Toolbox: undefined;
  Learning: undefined;
  Practice: undefined;
};

// 主题类型
export interface Theme {
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    accent: string;
    error: string;
    success: string;
    warning: string;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
  };
  typography: {
    h1: TextStyle;
    h2: TextStyle;
    h3: TextStyle;
    body: TextStyle;
    caption: TextStyle;
  };
}

// React Native TextStyle 导入
import { TextStyle } from 'react-native';

// 每日灵感类型
export interface DailyInspiration {
  id: string;
  date: string;
  message: string;
  card?: TarotCard | OracleCard;
  crystal?: Crystal;
  affirmation: string;
}

// 推荐内容类型
export interface RecommendedContent {
  id: string;
  title: string;
  type: 'tool' | 'learning' | 'practice';
  image: string;
  description: string;
  targetScreen: keyof RootStackParamList;
  params?: any;
}
