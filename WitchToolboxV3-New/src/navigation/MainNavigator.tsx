import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { RootStackParamList, TabParamList } from '../types';

// 导入屏幕组件
import HomeScreen from '../screens/HomeScreen';
import ToolboxScreen from '../screens/ToolboxScreen';
import LearningScreen from '../screens/LearningScreen';
import PracticeScreen from '../screens/PracticeScreen';

// 导入详情屏幕
import TarotReadingScreen from '../screens/tools/TarotReadingScreen';
import OracleReadingScreen from '../screens/tools/OracleReadingScreen';
import OHReadingScreen from '../screens/tools/OHReadingScreen';
import CrystalHealingScreen from '../screens/tools/CrystalHealingScreen';
import MeditationScreen from '../screens/tools/MeditationScreen';
import LearningDetailScreen from '../screens/LearningDetailScreen';
import PracticeDetailScreen from '../screens/PracticeDetailScreen';
import HistoryScreen from '../screens/HistoryScreen';
import SettingsScreen from '../screens/SettingsScreen';

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<TabParamList>();

// 底部Tab导航
const TabNavigator: React.FC = () => {
  const { theme } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'Home':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'Toolbox':
              iconName = focused ? 'library' : 'library-outline';
              break;
            case 'Learning':
              iconName = focused ? 'school' : 'school-outline';
              break;
            case 'Practice':
              iconName = focused ? 'fitness' : 'fitness-outline';
              break;
            default:
              iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.textSecondary,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopColor: theme.colors.primary + '20',
          borderTopWidth: 1,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerStyle: {
          backgroundColor: theme.colors.surface,
          shadowColor: 'transparent',
          elevation: 0,
        },
        headerTintColor: theme.colors.text,
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen} 
        options={{ 
          title: '首页',
          headerTitle: '女巫工具箱'
        }} 
      />
      <Tab.Screen 
        name="Toolbox" 
        component={ToolboxScreen} 
        options={{ 
          title: '工具箱',
          headerTitle: '占卜工具'
        }} 
      />
      <Tab.Screen 
        name="Learning" 
        component={LearningScreen} 
        options={{ 
          title: '学习',
          headerTitle: '学习中心'
        }} 
      />
      <Tab.Screen 
        name="Practice" 
        component={PracticeScreen} 
        options={{ 
          title: '实践',
          headerTitle: '实践指导'
        }} 
      />
    </Tab.Navigator>
  );
};

// 主导航器
const MainNavigator: React.FC = () => {
  const { theme } = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.surface,
          shadowColor: 'transparent',
          elevation: 0,
        },
        headerTintColor: theme.colors.text,
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 18,
        },
        headerBackTitleVisible: false,
        cardStyle: {
          backgroundColor: theme.colors.background,
        },
      }}
    >
      <Stack.Screen 
        name="Main" 
        component={TabNavigator} 
        options={{ headerShown: false }} 
      />
      
      {/* 工具详情屏幕 */}
      <Stack.Screen 
        name="TarotReading" 
        component={TarotReadingScreen} 
        options={{ 
          title: '塔罗牌占卜',
          presentation: 'modal'
        }} 
      />
      <Stack.Screen 
        name="OracleReading" 
        component={OracleReadingScreen} 
        options={{ 
          title: '神谕卡占卜',
          presentation: 'modal'
        }} 
      />
      <Stack.Screen 
        name="OHReading" 
        component={OHReadingScreen} 
        options={{ 
          title: 'OH卡占卜',
          presentation: 'modal'
        }} 
      />
      <Stack.Screen 
        name="CrystalHealing" 
        component={CrystalHealingScreen} 
        options={{ 
          title: '水晶疗愈',
          presentation: 'modal'
        }} 
      />
      <Stack.Screen 
        name="Meditation" 
        component={MeditationScreen} 
        options={{ 
          title: '冥想引导',
          presentation: 'modal'
        }} 
      />
      
      {/* 其他屏幕 */}
      <Stack.Screen 
        name="LearningDetail" 
        component={LearningDetailScreen} 
        options={{ title: '学习详情' }} 
      />
      <Stack.Screen 
        name="PracticeDetail" 
        component={PracticeDetailScreen} 
        options={{ title: '实践详情' }} 
      />
      <Stack.Screen 
        name="History" 
        component={HistoryScreen} 
        options={{ title: '占卜历史' }} 
      />
      <Stack.Screen 
        name="Settings" 
        component={SettingsScreen} 
        options={{ title: '设置' }} 
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
