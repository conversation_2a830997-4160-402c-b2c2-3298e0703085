#!/bin/bash

# 女巫工具箱V3 部署脚本

echo "🔮 女巫工具箱V3 部署脚本"
echo "=========================="

# 检查是否安装了必要的工具
check_dependencies() {
    echo "📋 检查依赖..."
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo "❌ npm 未安装，请先安装 npm"
        exit 1
    fi
    
    if ! command -v expo &> /dev/null; then
        echo "❌ Expo CLI 未安装，正在安装..."
        npm install -g @expo/cli
    fi
    
    echo "✅ 依赖检查完成"
}

# 安装项目依赖
install_dependencies() {
    echo "📦 安装项目依赖..."
    npm install
    echo "✅ 依赖安装完成"
}

# 启动开发服务器
start_dev() {
    echo "🚀 启动开发服务器..."
    echo "请使用 Expo Go 应用扫描二维码来预览应用"
    npm start
}

# 构建生产版本
build_production() {
    echo "🏗️ 构建生产版本..."
    
    # 检查是否安装了 EAS CLI
    if ! command -v eas &> /dev/null; then
        echo "📱 安装 EAS CLI..."
        npm install -g eas-cli
    fi
    
    echo "选择构建平台："
    echo "1) Android"
    echo "2) iOS"
    echo "3) 全平台"
    read -p "请输入选择 (1-3): " choice
    
    case $choice in
        1)
            echo "🤖 构建 Android 版本..."
            eas build --platform android
            ;;
        2)
            echo "🍎 构建 iOS 版本..."
            eas build --platform ios
            ;;
        3)
            echo "📱 构建全平台版本..."
            eas build --platform all
            ;;
        *)
            echo "❌ 无效选择"
            exit 1
            ;;
    esac
}

# 发布到 Expo
publish_expo() {
    echo "📤 发布到 Expo..."
    expo publish
    echo "✅ 发布完成"
}

# 主菜单
main_menu() {
    echo ""
    echo "请选择操作："
    echo "1) 检查并安装依赖"
    echo "2) 启动开发服务器"
    echo "3) 构建生产版本"
    echo "4) 发布到 Expo"
    echo "5) 完整部署流程"
    echo "6) 退出"
    
    read -p "请输入选择 (1-6): " choice
    
    case $choice in
        1)
            check_dependencies
            install_dependencies
            ;;
        2)
            start_dev
            ;;
        3)
            build_production
            ;;
        4)
            publish_expo
            ;;
        5)
            echo "🔄 执行完整部署流程..."
            check_dependencies
            install_dependencies
            publish_expo
            echo "✅ 部署完成！"
            ;;
        6)
            echo "👋 再见！"
            exit 0
            ;;
        *)
            echo "❌ 无效选择，请重新选择"
            main_menu
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "用法: ./deploy.sh [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -d, --dev      启动开发服务器"
    echo "  -b, --build    构建生产版本"
    echo "  -p, --publish  发布到 Expo"
    echo "  -f, --full     执行完整部署流程"
    echo ""
    echo "示例:"
    echo "  ./deploy.sh -d     # 启动开发服务器"
    echo "  ./deploy.sh -f     # 完整部署流程"
}

# 处理命令行参数
case "$1" in
    -h|--help)
        show_help
        ;;
    -d|--dev)
        check_dependencies
        start_dev
        ;;
    -b|--build)
        build_production
        ;;
    -p|--publish)
        publish_expo
        ;;
    -f|--full)
        check_dependencies
        install_dependencies
        publish_expo
        ;;
    "")
        main_menu
        ;;
    *)
        echo "❌ 未知选项: $1"
        show_help
        exit 1
        ;;
esac
