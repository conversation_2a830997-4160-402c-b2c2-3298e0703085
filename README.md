# 女巫工具箱 V3 🔮

一个现代化的身心灵占卜应用，基于 React Native 和 Expo 开发，提供塔罗牌、神谕卡、OH卡、水晶疗愈等多种占卜工具。

## ✨ 主要功能

### 🎴 占卜工具
- **塔罗牌占卜** - 支持多种牌阵（单张牌、三张牌、凯尔特十字等）
- **神谕卡占卜** - 接收来自天使和宇宙的指引
- **OH卡占卜** - 通过图像和词语的组合获得洞察
- **水晶疗愈** - 利用水晶的能量平衡身心灵
- **冥想引导** - 多种时长的冥想练习

### 📚 学习中心
- 塔罗牌基础教程
- 水晶疗愈入门
- 冥想与正念练习
- 占星学基础知识
- 进度追踪和成就系统

### 💼 实践指导
- **变现指导** - 如何将占卜技能转化为收入
- **自媒体运营** - 身心灵内容创作指南
- **实践技巧** - 日常修行和提升方法

### 🏠 个性化首页
- 每日灵感和肯定语
- 个性化推荐内容
- 快速访问常用工具
- 美观的轮播横幅

## 🛠 技术栈

- **React Native** - 跨平台移动应用开发
- **Expo** - 开发工具链和平台
- **TypeScript** - 类型安全的 JavaScript
- **React Navigation** - 导航管理
- **AsyncStorage** - 本地数据存储
- **Expo Linear Gradient** - 渐变效果
- **Expo Vector Icons** - 图标库

## 🚀 快速开始

### 环境要求
- Node.js 16+ 
- npm 或 yarn
- Expo CLI
- Expo Go 应用（用于真机测试）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd WitchToolboxV3-New
```

2. **安装依赖**
```bash
npm install
```

3. **启动开发服务器**
```bash
npm start
```

4. **在设备上运行**
- 下载 Expo Go 应用
- 扫描终端中显示的二维码
- 或按 `i` 在 iOS 模拟器中打开
- 或按 `a` 在 Android 模拟器中打开

## 📱 应用截图

### 主要界面
- 🏠 **首页** - 每日灵感、推荐内容、快速入口
- 🧰 **工具箱** - 各种占卜工具的集合
- 📖 **学习中心** - 系统化的学习内容
- 🎯 **实践指导** - 变现和技能提升指南

### 占卜工具
- 🎴 **塔罗牌** - 多种牌阵选择，详细解释
- ✨ **神谕卡** - 天使指引和宇宙信息
- 🖼 **OH卡** - 图像与词语的创意组合
- 💎 **水晶** - 能量疗愈和脉轮平衡
- 🧘 **冥想** - 引导式冥想练习

## 🎨 设计特色

### 深色神秘主题
- 深紫色主色调，营造神秘氛围
- 渐变背景和卡片设计
- 优雅的动画和过渡效果
- 响应式布局适配各种屏幕

### 用户体验
- 直观的导航结构
- 流畅的交互动画
- 个性化推荐系统
- 完整的历史记录管理

## 📂 项目结构

```
src/
├── components/          # 可复用组件
├── contexts/           # React Context
│   └── ThemeContext.tsx
├── data/              # 数据文件
│   ├── tarot/         # 塔罗牌数据
│   ├── oracle/        # 神谕卡数据
│   ├── crystals/      # 水晶数据
│   └── ...
├── navigation/        # 导航配置
│   └── MainNavigator.tsx
├── screens/          # 页面组件
│   ├── HomeScreen.tsx
│   ├── ToolboxScreen.tsx
│   ├── tools/        # 占卜工具页面
│   └── ...
├── types/            # TypeScript 类型定义
│   └── index.ts
└── utils/            # 工具函数
    ├── storage.ts    # 数据存储
    ├── cardUtils.ts  # 卡片工具
    └── ...
```

## 🔧 开发指南

### 添加新的占卜工具
1. 在 `src/data/` 中添加数据文件
2. 在 `src/screens/tools/` 中创建工具页面
3. 在 `MainNavigator.tsx` 中添加路由
4. 在 `ToolboxScreen.tsx` 中添加工具卡片

### 自定义主题
在 `ThemeContext.tsx` 中修改颜色和样式配置：
```typescript
const darkTheme: Theme = {
  colors: {
    primary: '#6B46C1',    // 主色调
    secondary: '#9333EA',   // 次要色
    background: '#0F0B1A',  // 背景色
    // ...
  }
}
```

### 数据存储
使用 AsyncStorage 进行本地数据持久化：
```typescript
import { saveDivinationResult, getDivinationHistory } from '../utils/storage';

// 保存占卜结果
await saveDivinationResult(result);

// 获取历史记录
const history = await getDivinationHistory();
```

## 🚀 部署

### 构建生产版本
```bash
# 构建 Android APK
expo build:android

# 构建 iOS IPA
expo build:ios

# 或使用 EAS Build（推荐）
eas build --platform all
```

### 发布到应用商店
```bash
# 发布到 Expo
expo publish

# 提交到应用商店
eas submit --platform all
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 感谢所有贡献者的支持
- 特别感谢身心灵社区的反馈和建议
- 图标来源：Expo Vector Icons
- 设计灵感：现代神秘主义美学

## 📞 联系我们

- 项目主页：[GitHub Repository]
- 问题反馈：[GitHub Issues]
- 邮箱：<EMAIL>

---

**女巫工具箱 V3** - 探索内在智慧，连接宇宙能量 ✨🔮✨
