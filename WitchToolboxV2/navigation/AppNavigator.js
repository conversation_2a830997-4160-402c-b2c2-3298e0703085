import React from 'react';
import { Text } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';

// 导入屏幕
import HomeScreen from '../screens/HomeScreen';
import ToolboxScreen from '../screens/ToolboxScreen';
import LearningScreen from '../screens/LearningScreen';
import PracticeScreen from '../screens/PracticeScreen';
import TarotReadingScreen from '../screens/TarotReadingScreen';
import OracleReadingScreen from '../screens/OracleReadingScreen';
import NumerologyScreen from '../screens/NumerologyScreen';
import OHCardScreen from '../screens/OHCardScreen';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// 底部标签导航
function TabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: '#6B46C1',
        tabBarInactiveTintColor: '#9CA3AF',
        tabBarStyle: {
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        headerShown: false,
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: '首页',
          tabBarIcon: ({ color, size }) => (
            <Text style={{ fontSize: size, color }}>🏠</Text>
          ),
        }}
      />
      <Tab.Screen
        name="Toolbox"
        component={ToolboxScreen}
        options={{
          title: '工具箱',
          tabBarIcon: ({ color, size }) => (
            <Text style={{ fontSize: size, color }}>🧰</Text>
          ),
        }}
      />
      <Tab.Screen
        name="Learning"
        component={LearningScreen}
        options={{
          title: '学习',
          tabBarIcon: ({ color, size }) => (
            <Text style={{ fontSize: size, color }}>📚</Text>
          ),
        }}
      />
      <Tab.Screen
        name="Practice"
        component={PracticeScreen}
        options={{
          title: '实践',
          tabBarIcon: ({ color, size }) => (
            <Text style={{ fontSize: size, color }}>💼</Text>
          ),
        }}
      />
    </Tab.Navigator>
  );
}

// 主导航器
export default function AppNavigator() {
  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen 
          name="MainTabs" 
          component={TabNavigator} 
        />
        <Stack.Screen 
          name="TarotReading" 
          component={TarotReadingScreen}
          options={{
            headerShown: false,
            presentation: 'modal',
          }}
        />
        <Stack.Screen
          name="OracleReading"
          component={OracleReadingScreen}
          options={{
            headerShown: false,
            presentation: 'modal',
          }}
        />
        <Stack.Screen
          name="Numerology"
          component={NumerologyScreen}
          options={{
            headerShown: false,
            presentation: 'modal',
          }}
        />
        <Stack.Screen
          name="OHCard"
          component={OHCardScreen}
          options={{
            headerShown: false,
            presentation: 'modal',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
