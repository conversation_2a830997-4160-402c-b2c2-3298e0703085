import React, { useState } from 'react';
import { View, TextInput, Text, StyleSheet } from 'react-native';
import { Colors, Typography, Spacing, BorderRadius } from '../../constants/theme';

export const Input = ({ 
  label = '',
  placeholder = '',
  value = '',
  onChangeText = () => {},
  error = '',
  multiline = false,
  numberOfLines = 1,
  keyboardType = 'default',
  secureTextEntry = false,
  style = {},
  inputStyle = {},
  ...props 
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const getInputStyle = () => {
    const baseStyle = {
      borderWidth: 2,
      borderRadius: BorderRadius.lg,
      paddingVertical: Spacing.md,
      paddingHorizontal: Spacing.md,
      fontSize: Typography.fontSize.base,
      color: Colors.text.primary,
      backgroundColor: Colors.background.card,
    };

    let borderColor = Colors.border.light;
    if (error) {
      borderColor = Colors.error;
    } else if (isFocused) {
      borderColor = Colors.primary[600];
    }

    const multilineStyle = multiline ? {
      minHeight: numberOfLines * 24 + Spacing.md * 2,
      textAlignVertical: 'top',
    } : {};

    return [
      baseStyle,
      { borderColor },
      multilineStyle,
      inputStyle,
    ];
  };

  return (
    <View style={[styles.container, style]}>
      {label ? (
        <Text style={styles.label}>{label}</Text>
      ) : null}
      
      <TextInput
        style={getInputStyle()}
        placeholder={placeholder}
        placeholderTextColor={Colors.text.tertiary}
        value={value}
        onChangeText={onChangeText}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        multiline={multiline}
        numberOfLines={numberOfLines}
        keyboardType={keyboardType}
        secureTextEntry={secureTextEntry}
        {...props}
      />
      
      {error ? (
        <Text style={styles.errorText}>{error}</Text>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.md,
  },
  label: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.text.primary,
    marginBottom: Spacing.sm,
  },
  errorText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.error,
    marginTop: Spacing.xs,
  },
});

export default Input;
