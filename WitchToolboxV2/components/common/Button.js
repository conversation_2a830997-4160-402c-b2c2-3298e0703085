import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors, Typography, Spacing, BorderRadius, Shadows } from '../../constants/theme';

export const Button = ({ 
  title, 
  onPress, 
  variant = 'primary', 
  size = 'medium',
  disabled = false,
  gradient = null,
  style = {},
  textStyle = {},
  ...props 
}) => {
  const getButtonStyle = () => {
    const baseStyle = {
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: BorderRadius.lg,
    };

    const sizeStyles = {
      small: {
        paddingVertical: Spacing.sm,
        paddingHorizontal: Spacing.md,
      },
      medium: {
        paddingVertical: Spacing.md,
        paddingHorizontal: Spacing.lg,
      },
      large: {
        paddingVertical: Spacing.lg,
        paddingHorizontal: Spacing.xl,
      },
    };

    const variantStyles = {
      primary: {
        backgroundColor: Colors.primary[600],
        ...Shadows.md,
      },
      secondary: {
        backgroundColor: Colors.neutral[100],
        borderWidth: 1,
        borderColor: Colors.border.light,
      },
      ghost: {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: Colors.primary[600],
      },
      danger: {
        backgroundColor: Colors.error,
        ...Shadows.md,
      },
    };

    const disabledStyle = disabled ? {
      opacity: 0.5,
    } : {};

    return [
      baseStyle,
      sizeStyles[size],
      variantStyles[variant],
      disabledStyle,
      style,
    ];
  };

  const getTextStyle = () => {
    const baseTextStyle = {
      fontWeight: Typography.fontWeight.semibold,
    };

    const sizeTextStyles = {
      small: {
        fontSize: Typography.fontSize.sm,
      },
      medium: {
        fontSize: Typography.fontSize.base,
      },
      large: {
        fontSize: Typography.fontSize.lg,
      },
    };

    const variantTextStyles = {
      primary: {
        color: Colors.text.inverse,
      },
      secondary: {
        color: Colors.text.primary,
      },
      ghost: {
        color: Colors.primary[600],
      },
      danger: {
        color: Colors.text.inverse,
      },
    };

    return [
      baseTextStyle,
      sizeTextStyles[size],
      variantTextStyles[variant],
      textStyle,
    ];
  };

  const ButtonContent = () => (
    <Text style={getTextStyle()}>{title}</Text>
  );

  if (gradient) {
    return (
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled}
        style={[{ borderRadius: BorderRadius.lg, overflow: 'hidden' }, style]}
        {...props}
      >
        <LinearGradient
          colors={gradient}
          style={getButtonStyle()}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <ButtonContent />
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={getButtonStyle()}
      onPress={onPress}
      disabled={disabled}
      {...props}
    >
      <ButtonContent />
    </TouchableOpacity>
  );
};

export default Button;
