import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors, BorderRadius, Shadows, Spacing } from '../../constants/theme';

export const Card = ({ 
  children, 
  variant = 'default',
  onPress = null,
  gradient = null,
  style = {},
  ...props 
}) => {
  const getCardStyle = () => {
    const baseStyle = {
      borderRadius: BorderRadius.xl,
      padding: Spacing.lg,
    };

    const variantStyles = {
      default: {
        backgroundColor: Colors.background.card,
        ...Shadows.md,
      },
      elevated: {
        backgroundColor: Colors.background.card,
        ...Shadows.lg,
      },
      flat: {
        backgroundColor: Colors.background.card,
        borderWidth: 1,
        borderColor: Colors.border.light,
      },
      transparent: {
        backgroundColor: 'transparent',
      },
    };

    return [
      baseStyle,
      variantStyles[variant],
      style,
    ];
  };

  const CardContent = () => children;

  if (gradient) {
    const Component = onPress ? TouchableOpacity : View;
    return (
      <Component
        onPress={onPress}
        style={[{ borderRadius: BorderRadius.xl, overflow: 'hidden' }, style]}
        {...props}
      >
        <LinearGradient
          colors={gradient}
          style={[{ padding: Spacing.lg }, style]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <CardContent />
        </LinearGradient>
      </Component>
    );
  }

  if (onPress) {
    return (
      <TouchableOpacity
        style={getCardStyle()}
        onPress={onPress}
        {...props}
      >
        <CardContent />
      </TouchableOpacity>
    );
  }

  return (
    <View style={getCardStyle()} {...props}>
      <CardContent />
    </View>
  );
};

export default Card;
