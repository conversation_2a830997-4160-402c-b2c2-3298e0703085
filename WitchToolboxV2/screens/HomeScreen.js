import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Dimensions
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Card, Button } from '../components/common';
import { Colors, Typography, Spacing, BorderRadius, Shadows } from '../constants/theme';

const { width } = Dimensions.get('window');

export default function HomeScreen({ navigation }) {
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [dailyInspiration, setDailyInspiration] = useState('');

  const banners = [
    {
      id: 1,
      title: '探索塔罗的奥秘',
      subtitle: '让古老的智慧指引你的人生',
      gradient: ['#667eea', '#764ba2'],
      action: () => navigation.navigate('Toolbox'),
    },
    {
      id: 2,
      title: '水晶疗愈之旅',
      subtitle: '感受大自然的神奇能量',
      gradient: ['#f093fb', '#f5576c'],
      action: () => navigation.navigate('Toolbox'),
    },
    {
      id: 3,
      title: '占星学的智慧',
      subtitle: '星空中隐藏的人生密码',
      gradient: ['#4facfe', '#00f2fe'],
      action: () => navigation.navigate('Toolbox'),
    },
  ];

  const inspirations = [
    "相信你内在的智慧，它比任何外在的声音都更了解你的真实需求。",
    "每一次占卜都是与宇宙的对话，倾听它想要告诉你的信息。",
    "水晶不仅仅是美丽的石头，它们是大地母亲给予我们的疗愈礼物。",
    "星星不会为任何人改变轨道，但它们会为每个人照亮前行的路。",
    "真正的魔法不在于改变外在世界，而在于转化内在的自己。",
  ];

  const quickActions = [
    {
      id: 'tarot',
      title: '塔罗占卜',
      icon: '🃏',
      gradient: ['#667eea', '#764ba2'],
      onPress: () => navigation.navigate('TarotReading'),
    },
    {
      id: 'oracle',
      title: '神谕卡',
      icon: '🔮',
      gradient: ['#f093fb', '#f5576c'],
      onPress: () => navigation.navigate('OracleReading'),
    },
    {
      id: 'toolbox',
      title: '工具箱',
      icon: '🧰',
      gradient: ['#4facfe', '#00f2fe'],
      onPress: () => navigation.navigate('Toolbox'),
    },
    {
      id: 'learning',
      title: '学习',
      icon: '📚',
      gradient: ['#fa709a', '#fee140'],
      onPress: () => navigation.navigate('Learning'),
    },
  ];

  useEffect(() => {
    // 轮播Banner自动切换
    const bannerTimer = setInterval(() => {
      setCurrentBannerIndex((prevIndex) =>
        prevIndex === banners.length - 1 ? 0 : prevIndex + 1
      );
    }, 4000);

    // 设置每日灵感
    const today = new Date().getDate();
    setDailyInspiration(inspirations[today % inspirations.length]);

    return () => clearInterval(bannerTimer);
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 轮播Banner */}
        <View style={styles.bannerContainer}>
          <TouchableOpacity
            style={styles.banner}
            onPress={banners[currentBannerIndex].action}
          >
            <LinearGradient
              colors={banners[currentBannerIndex].gradient}
              style={styles.bannerGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.bannerTitle}>
                {banners[currentBannerIndex].title}
              </Text>
              <Text style={styles.bannerSubtitle}>
                {banners[currentBannerIndex].subtitle}
              </Text>
            </LinearGradient>
          </TouchableOpacity>

          <View style={styles.bannerIndicators}>
            {banners.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.indicator,
                  index === currentBannerIndex && styles.activeIndicator,
                ]}
              />
            ))}
          </View>
        </View>

        <View style={styles.content}>
          {/* 每日灵感 */}
          <Card style={styles.dailyCard}>
            <Text style={styles.dailyTitle}>✨ 今日灵感</Text>
            <Text style={styles.dailyMessage}>{dailyInspiration}</Text>
          </Card>

          {/* 快捷按钮 */}
          <Text style={styles.sectionTitle}>快速开始</Text>
          <View style={styles.quickActionsContainer}>
            {quickActions.map((action) => (
              <TouchableOpacity
                key={action.id}
                style={styles.quickActionButton}
                onPress={action.onPress}
              >
                <LinearGradient
                  colors={action.gradient}
                  style={styles.quickActionGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Text style={styles.quickActionIcon}>{action.icon}</Text>
                  <Text style={styles.quickActionTitle}>{action.title}</Text>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>

          {/* 推荐内容 */}
          <Text style={styles.sectionTitle}>推荐内容</Text>
          <Card onPress={() => {}} style={styles.recommendCard}>
            <Text style={styles.recommendTitle}>🌙 月相与占卜</Text>
            <Text style={styles.recommendDescription}>
              了解月相如何影响你的占卜准确性
            </Text>
          </Card>

          <Card onPress={() => {}} style={styles.recommendCard}>
            <Text style={styles.recommendTitle}>💎 水晶入门指南</Text>
            <Text style={styles.recommendDescription}>
              从零开始学习水晶的选择和使用方法
            </Text>
          </Card>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  bannerContainer: {
    marginBottom: 20,
  },
  banner: {
    height: 200,
    marginHorizontal: Spacing.lg,
    marginTop: Spacing.lg,
    borderRadius: BorderRadius.xl,
    overflow: 'hidden',
  },
  bannerGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  bannerTitle: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.inverse,
    textAlign: 'center',
    marginBottom: Spacing.sm,
  },
  bannerSubtitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.inverse,
    textAlign: 'center',
    opacity: 0.9,
  },
  bannerIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#d1d5db',
    marginHorizontal: 4,
  },
  activeIndicator: {
    backgroundColor: '#6366f1',
  },
  content: {
    paddingHorizontal: 20,
  },
  dailyCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
    borderLeftWidth: 4,
    borderLeftColor: '#6366f1',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  dailyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  dailyMessage: {
    fontSize: 16,
    color: '#6b7280',
    lineHeight: 24,
    fontStyle: 'italic',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  quickActionButton: {
    width: (width - 60) / 2,
    height: 100,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 12,
  },
  quickActionGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quickActionIcon: {
    fontSize: 28,
    marginBottom: 8,
  },
  quickActionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  recommendCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  recommendTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  recommendDescription: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
});
