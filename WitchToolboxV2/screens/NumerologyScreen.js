import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Card, Button, Input, LoadingSpinner } from '../components/common';
import { Colors, Typography, Spacing, BorderRadius, Shadows } from '../constants/theme';

export default function NumerologyScreen({ navigation }) {
  const [birthDate, setBirthDate] = useState('');
  const [result, setResult] = useState(null);
  const [isCalculating, setIsCalculating] = useState(false);

  const calculateLifePath = (dateString) => {
    // 简单的生命路径数计算
    const numbers = dateString.replace(/\D/g, '');
    let sum = 0;
    for (let i = 0; i < numbers.length; i++) {
      sum += parseInt(numbers[i]);
    }
    
    // 减少到单个数字（除了11, 22, 33这些主数字）
    while (sum > 9 && sum !== 11 && sum !== 22 && sum !== 33) {
      const temp = sum;
      sum = 0;
      while (temp > 0) {
        sum += temp % 10;
        temp = Math.floor(temp / 10);
      }
    }
    
    return sum;
  };

  const getNumberMeaning = (number) => {
    const meanings = {
      1: {
        title: '领导者',
        description: '你是天生的领导者，具有独立、创新和开拓的精神。',
        traits: ['独立', '创新', '领导力', '自信'],
        color: '#ef4444',
      },
      2: {
        title: '合作者',
        description: '你善于合作，具有外交天赋和和谐的天性。',
        traits: ['合作', '外交', '和谐', '敏感'],
        color: '#f97316',
      },
      3: {
        title: '创造者',
        description: '你充满创造力，善于表达和沟通。',
        traits: ['创造力', '表达力', '乐观', '艺术天赋'],
        color: '#eab308',
      },
      4: {
        title: '建设者',
        description: '你务实可靠，善于建立稳固的基础。',
        traits: ['务实', '可靠', '组织力', '耐心'],
        color: '#22c55e',
      },
      5: {
        title: '自由者',
        description: '你热爱自由，喜欢冒险和变化。',
        traits: ['自由', '冒险', '好奇', '多才多艺'],
        color: '#06b6d4',
      },
      6: {
        title: '照顾者',
        description: '你有强烈的责任感，善于照顾他人。',
        traits: ['责任感', '关爱', '家庭观念', '治愈能力'],
        color: '#3b82f6',
      },
      7: {
        title: '寻求者',
        description: '你是精神的寻求者，喜欢深入思考。',
        traits: ['智慧', '直觉', '神秘', '分析力'],
        color: '#8b5cf6',
      },
      8: {
        title: '成就者',
        description: '你有强烈的成就欲望，善于管理和组织。',
        traits: ['成就', '权威', '管理', '物质成功'],
        color: '#ec4899',
      },
      9: {
        title: '人道主义者',
        description: '你有博爱精神，关心人类的福祉。',
        traits: ['博爱', '慈善', '智慧', '完成'],
        color: '#f43f5e',
      },
      11: {
        title: '直觉大师',
        description: '你拥有强大的直觉和精神洞察力。',
        traits: ['直觉', '灵性', '启发', '敏感'],
        color: '#a855f7',
      },
      22: {
        title: '大建筑师',
        description: '你有能力将梦想变为现实。',
        traits: ['远见', '实现', '建设', '影响力'],
        color: '#059669',
      },
      33: {
        title: '大师教师',
        description: '你是天生的老师和治愈者。',
        traits: ['教导', '治愈', '服务', '慈悲'],
        color: '#dc2626',
      },
    };
    
    return meanings[number] || meanings[1];
  };

  const handleCalculate = () => {
    if (!birthDate || birthDate.length < 8) {
      Alert.alert('提示', '请输入完整的出生日期（如：19900101）');
      return;
    }

    setIsCalculating(true);
    
    // 模拟计算过程
    setTimeout(() => {
      const lifePathNumber = calculateLifePath(birthDate);
      const meaning = getNumberMeaning(lifePathNumber);
      
      setResult({
        number: lifePathNumber,
        ...meaning,
      });
      setIsCalculating(false);
    }, 1500);
  };

  const renderResult = () => {
    if (!result) return null;

    return (
      <View style={styles.resultContainer}>
        <LinearGradient
          colors={[result.color, result.color + '80']}
          style={styles.resultGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.numberCircle}>
            <Text style={styles.resultNumber}>{result.number}</Text>
          </View>
          <Text style={styles.resultTitle}>{result.title}</Text>
          <Text style={styles.resultDescription}>{result.description}</Text>
          
          <View style={styles.traitsContainer}>
            <Text style={styles.traitsTitle}>核心特质：</Text>
            <View style={styles.traitsGrid}>
              {result.traits.map((trait, index) => (
                <View key={index} style={styles.traitTag}>
                  <Text style={styles.traitText}>{trait}</Text>
                </View>
              ))}
            </View>
          </View>
        </LinearGradient>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backText}>← 返回</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>数字占卜</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.introCard}>
          <Text style={styles.introTitle}>🔢 生命路径数</Text>
          <Text style={styles.introText}>
            通过你的出生日期，计算出你的生命路径数，
            揭示你的人生使命和核心特质。
          </Text>
        </Card>

        <Card style={styles.inputCard}>
          <Input
            label="请输入你的出生日期"
            placeholder="例如：19900101"
            value={birthDate}
            onChangeText={setBirthDate}
            keyboardType="numeric"
            maxLength={8}
          />
          <Text style={styles.inputHint}>格式：年月日（8位数字）</Text>

          <Button
            title={isCalculating ? '计算中...' : '开始计算'}
            onPress={handleCalculate}
            disabled={isCalculating}
            variant="primary"
            style={styles.calculateButton}
          />

          {isCalculating && (
            <View style={styles.loadingContainer}>
              <LoadingSpinner />
              <Text style={styles.loadingText}>正在解读你的生命密码...</Text>
            </View>
          )}
        </Card>

        {renderResult()}

        <Card style={styles.tipsCard}>
          <Text style={styles.tipsTitle}>💡 数字占卜小贴士</Text>
          <Text style={styles.tipText}>• 生命路径数反映你的人生主题和使命</Text>
          <Text style={styles.tipText}>• 每个数字都有其独特的能量和特质</Text>
          <Text style={styles.tipText}>• 11、22、33被称为主数字，具有特殊意义</Text>
          <Text style={styles.tipText}>• 了解自己的数字有助于更好地认识自己</Text>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  loadingContainer: {
    alignItems: 'center',
    marginTop: Spacing.lg,
  },
  loadingText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    marginTop: Spacing.sm,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  backButton: {
    padding: 8,
  },
  backText: {
    fontSize: 16,
    color: '#6366f1',
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  placeholder: {
    width: 60,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  introCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  introTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
    textAlign: 'center',
  },
  introText: {
    fontSize: 16,
    color: '#6b7280',
    lineHeight: 24,
    textAlign: 'center',
  },
  inputCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  dateInput: {
    borderWidth: 2,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 16,
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 8,
  },
  inputHint: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 20,
  },
  calculateButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  calculatingButton: {
    backgroundColor: '#9ca3af',
  },
  calculateButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  resultContainer: {
    marginBottom: 20,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  resultGradient: {
    padding: 24,
    alignItems: 'center',
  },
  numberCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  resultNumber: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#fff',
  },
  resultTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 12,
  },
  resultDescription: {
    fontSize: 16,
    color: '#fff',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
    opacity: 0.9,
  },
  traitsContainer: {
    width: '100%',
  },
  traitsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 12,
    textAlign: 'center',
  },
  traitsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 8,
  },
  traitTag: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    margin: 2,
  },
  traitText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  tipsCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#6366f1',
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
    marginBottom: 8,
  },
});
