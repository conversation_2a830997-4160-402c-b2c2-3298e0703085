import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

export default function PracticeScreen({ navigation }) {
  const practiceModules = [
    {
      id: 'manifestation',
      title: '变现指导',
      description: '学习如何将愿望转化为现实',
      icon: '✨',
      gradient: ['#667eea', '#764ba2'],
      features: ['愿望清单制作', '能量对齐技巧', '显化仪式指导'],
    },
    {
      id: 'social-media',
      title: '自媒体运营学习',
      description: '掌握神秘学内容创作与传播',
      icon: '📱',
      gradient: ['#f093fb', '#f5576c'],
      features: ['内容策划', '粉丝互动', '品牌建设'],
    },
    {
      id: 'practice-guide',
      title: '实践指导',
      description: '系统化的神秘学实践方法',
      icon: '🎯',
      gradient: ['#4facfe', '#00f2fe'],
      features: ['日常修行', '能量管理', '直觉开发'],
    },
  ];

  const renderPracticeCard = (module) => (
    <TouchableOpacity key={module.id} style={styles.moduleCard}>
      <LinearGradient
        colors={module.gradient}
        style={styles.cardGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.cardHeader}>
          <Text style={styles.moduleIcon}>{module.icon}</Text>
          <View style={styles.moduleInfo}>
            <Text style={styles.moduleTitle}>{module.title}</Text>
            <Text style={styles.moduleDescription}>{module.description}</Text>
          </View>
        </View>
        
        <View style={styles.featuresContainer}>
          {module.features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <Text style={styles.featureBullet}>•</Text>
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>
        
        <View style={styles.cardFooter}>
          <Text style={styles.actionText}>点击开始学习</Text>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>实践</Text>
          <Text style={styles.headerSubtitle}>将神秘学智慧应用到现实生活中</Text>
        </View>

        <View style={styles.introSection}>
          <Text style={styles.introTitle}>实践的力量</Text>
          <Text style={styles.introText}>
            真正的智慧来自于实践。在这里，你将学会如何将神秘学的知识转化为实际的生活技能，
            包括愿望显化、自媒体运营以及日常的灵性修行。让古老的智慧为现代生活服务。
          </Text>
        </View>

        <View style={styles.modulesContainer}>
          {practiceModules.map(renderPracticeCard)}
        </View>

        <View style={styles.tipsSection}>
          <Text style={styles.tipsTitle}>💡 实践小贴士</Text>
          <View style={styles.tipsList}>
            <Text style={styles.tipItem}>• 保持每日的修行习惯，哪怕只有5分钟</Text>
            <Text style={styles.tipItem}>• 记录你的实践体验和感悟</Text>
            <Text style={styles.tipItem}>• 与志同道合的朋友分享交流</Text>
            <Text style={styles.tipItem}>• 相信过程，耐心等待结果显现</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    backgroundColor: '#fff',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  introSection: {
    backgroundColor: '#fff',
    margin: 20,
    padding: 20,
    borderRadius: 16,
    marginBottom: 30,
  },
  introTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  introText: {
    fontSize: 16,
    color: '#6b7280',
    lineHeight: 24,
  },
  modulesContainer: {
    paddingHorizontal: 20,
    gap: 20,
  },
  moduleCard: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  cardGradient: {
    padding: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  moduleIcon: {
    fontSize: 40,
    marginRight: 16,
  },
  moduleInfo: {
    flex: 1,
  },
  moduleTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  moduleDescription: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
  },
  featuresContainer: {
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureBullet: {
    color: '#fff',
    fontSize: 16,
    marginRight: 8,
  },
  featureText: {
    color: '#fff',
    fontSize: 14,
    opacity: 0.9,
  },
  cardFooter: {
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  actionText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  tipsSection: {
    backgroundColor: '#fff',
    margin: 20,
    padding: 20,
    borderRadius: 16,
    marginTop: 30,
  },
  tipsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  tipsList: {
    gap: 8,
  },
  tipItem: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
});
