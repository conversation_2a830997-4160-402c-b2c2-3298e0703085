import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

export default function LearningScreen() {
  const divinerTraining = [
    {
      id: 'professional-tarot',
      title: '专业塔罗占卜师认证',
      description: '从入门到精通，成为专业的塔罗占卜师',
      level: '初级到高级',
      duration: '3个月',
      price: '¥2999',
      features: ['78张牌意详解', '牌阵技巧', '客户咨询技巧', '职业规划'],
      gradient: ['#667eea', '#764ba2'],
    },
    {
      id: 'oracle-master',
      title: '神谕卡大师课程',
      description: '深入学习神谕卡的神圣智慧',
      level: '中级',
      duration: '2个月',
      price: '¥1999',
      features: ['多套神谕卡系统', '直觉开发', '能量感知', '灵性指导'],
      gradient: ['#f093fb', '#f5576c'],
    },
    {
      id: 'crystal-healer',
      title: '水晶疗愈师培训',
      description: '学习水晶的疗愈能量和使用方法',
      level: '初级到中级',
      duration: '6周',
      price: '¥1599',
      features: ['水晶知识', '脉轮平衡', '能量清理', '疗愈技巧'],
      gradient: ['#4facfe', '#00f2fe'],
    },
  ];

  const otherCourses = [
    {
      id: 'numerology-basic',
      title: '数字占卜入门',
      description: '探索数字的神秘力量',
      progress: 0,
      lessons: 10,
      type: 'free',
    },
    {
      id: 'astrology-basic',
      title: '占星学基础',
      description: '了解星座和行星的奥秘',
      progress: 0,
      lessons: 15,
      type: 'free',
    },
    {
      id: 'meditation-guide',
      title: '冥想引导技巧',
      description: '学习如何引导他人进入冥想状态',
      progress: 0,
      lessons: 8,
      type: 'premium',
    },
    {
      id: 'energy-healing',
      title: '能量疗愈基础',
      description: '掌握基本的能量疗愈技术',
      progress: 0,
      lessons: 12,
      type: 'premium',
    },
  ];

  const renderTrainingCard = (training) => (
    <TouchableOpacity key={training.id} style={styles.trainingCard}>
      <LinearGradient
        colors={training.gradient}
        style={styles.trainingGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.trainingHeader}>
          <Text style={styles.trainingTitle}>{training.title}</Text>
          <Text style={styles.trainingPrice}>{training.price}</Text>
        </View>
        <Text style={styles.trainingDescription}>{training.description}</Text>
        <View style={styles.trainingInfo}>
          <Text style={styles.trainingLevel}>难度: {training.level}</Text>
          <Text style={styles.trainingDuration}>时长: {training.duration}</Text>
        </View>
        <View style={styles.featuresContainer}>
          {training.features.map((feature, index) => (
            <Text key={index} style={styles.featureText}>• {feature}</Text>
          ))}
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );

  const renderCourseModule = (course) => (
    <TouchableOpacity key={course.id} style={styles.courseCard}>
      <View style={styles.courseHeader}>
        <Text style={styles.courseTitle}>{course.title}</Text>
        <View style={styles.courseTypeContainer}>
          <Text style={[
            styles.courseType,
            course.type === 'free' ? styles.freeType : styles.premiumType
          ]}>
            {course.type === 'free' ? '免费' : '付费'}
          </Text>
        </View>
      </View>
      <Text style={styles.courseDescription}>{course.description}</Text>
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              { width: `${course.progress}%` }
            ]}
          />
        </View>
        <Text style={styles.progressText}>{course.progress}% • {course.lessons}课</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>学习</Text>
          <Text style={styles.headerSubtitle}>提升你的神秘学技能</Text>
        </View>

        {/* 占卜师培训 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🎓 占卜师培训</Text>
          <Text style={styles.sectionDescription}>
            专业的占卜师认证课程，从入门到精通
          </Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.trainingScrollView}
          >
            {divinerTraining.map(renderTrainingCard)}
          </ScrollView>
        </View>

        {/* 其他教程 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📚 其他教程</Text>
          <Text style={styles.sectionDescription}>
            丰富的免费和付费课程，满足不同学习需求
          </Text>
          {otherCourses.map(renderCourseModule)}
        </View>

        {/* 学习统计 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📊 学习统计</Text>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>0</Text>
              <Text style={styles.statLabel}>已完成课程</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>0</Text>
              <Text style={styles.statLabel}>学习天数</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>0</Text>
              <Text style={styles.statLabel}>获得证书</Text>
            </View>
          </View>
        </View>

        {/* 学习工具 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🛠️ 学习工具</Text>
          <TouchableOpacity style={styles.toolCard}>
            <Text style={styles.toolTitle}>📝 学习笔记</Text>
            <Text style={styles.toolDescription}>记录你的学习心得和重要知识点</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.toolCard}>
            <Text style={styles.toolTitle}>🎯 技能测试</Text>
            <Text style={styles.toolDescription}>测试你的占卜技能掌握程度</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.toolCard}>
            <Text style={styles.toolTitle}>👥 学习社区</Text>
            <Text style={styles.toolDescription}>与其他学习者交流经验和心得</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2d3436',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#636e72',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6c5ce7',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#636e72',
    textAlign: 'center',
  },
  modulesContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2d3436',
    marginBottom: 16,
  },
  moduleCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  moduleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  moduleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2d3436',
    flex: 1,
  },
  lessonCount: {
    fontSize: 12,
    color: '#6c5ce7',
    backgroundColor: '#f1f0ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  moduleDescription: {
    fontSize: 14,
    color: '#636e72',
    marginBottom: 12,
    lineHeight: 20,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: '#e9ecef',
    borderRadius: 3,
    marginRight: 12,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#6c5ce7',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: '#636e72',
    minWidth: 30,
  },
  practiceContainer: {
    marginBottom: 24,
  },
  practiceCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  practiceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2d3436',
    marginBottom: 4,
  },
  practiceDescription: {
    fontSize: 14,
    color: '#636e72',
    lineHeight: 20,
  },
});
