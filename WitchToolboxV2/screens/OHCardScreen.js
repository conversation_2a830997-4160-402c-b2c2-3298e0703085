import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

export default function OHCardScreen({ navigation }) {
  const [topic, setTopic] = useState('');
  const [selectedImageCard, setSelectedImageCard] = useState(null);
  const [selectedWordCard, setSelectedWordCard] = useState(null);
  const [interpretation, setInterpretation] = useState('');
  const [isDrawing, setIsDrawing] = useState(false);

  // 模拟图像卡数据
  const imageCards = [
    { id: 1, name: '自由之鸟', description: '展翅高飞的鸟儿，象征自由与解脱' },
    { id: 2, name: '深邃之眼', description: '洞察一切的眼睛，代表智慧与直觉' },
    { id: 3, name: '生命之树', description: '茂盛的大树，象征成长与根基' },
    { id: 4, name: '流水', description: '潺潺流水，代表变化与流动' },
    { id: 5, name: '山峰', description: '巍峨山峰，象征挑战与成就' },
    { id: 6, name: '花朵', description: '绽放的花朵，代表美丽与新生' },
  ];

  // 模拟词语卡数据
  const wordCards = [
    { id: 1, word: '爱', meaning: '无条件的关怀与接纳' },
    { id: 2, word: '勇气', meaning: '面对困难的内在力量' },
    { id: 3, word: '智慧', meaning: '深刻的理解与洞察' },
    { id: 4, word: '和谐', meaning: '平衡与协调的状态' },
    { id: 5, word: '创造', meaning: '带来新事物的能力' },
    { id: 6, word: '转化', meaning: '改变与成长的过程' },
  ];

  const generateInterpretation = (imageCard, wordCard, userTopic) => {
    const interpretations = [
      `${imageCard.name}与"${wordCard.word}"的结合，暗示着在${userTopic || '你关心的问题'}中，你需要像${imageCard.description}一样，以${wordCard.meaning}的态度去面对。这个组合提醒你，真正的解决方案往往来自内心的智慧。`,
      
      `当${imageCard.name}遇见"${wordCard.word}"，它告诉我们关于${userTopic || '当前情况'}的深层信息。${imageCard.description}代表了你当前的状态，而${wordCard.meaning}则是你需要培养的品质。`,
      
      `这个组合——${imageCard.name}和"${wordCard.word}"——为你的${userTopic || '问题'}提供了独特的视角。${imageCard.description}反映了你的潜意识状态，${wordCard.meaning}则指向了前进的方向。`,
    ];
    
    return interpretations[Math.floor(Math.random() * interpretations.length)];
  };

  const drawCards = () => {
    if (!topic.trim()) {
      alert('请先输入你想探索的主题');
      return;
    }

    setIsDrawing(true);
    
    // 模拟抽卡动画
    setTimeout(() => {
      const randomImageCard = imageCards[Math.floor(Math.random() * imageCards.length)];
      const randomWordCard = wordCards[Math.floor(Math.random() * wordCards.length)];
      
      setSelectedImageCard(randomImageCard);
      setSelectedWordCard(randomWordCard);
      
      const newInterpretation = generateInterpretation(randomImageCard, randomWordCard, topic);
      setInterpretation(newInterpretation);
      
      setIsDrawing(false);
    }, 2000);
  };

  const resetCards = () => {
    setSelectedImageCard(null);
    setSelectedWordCard(null);
    setInterpretation('');
    setTopic('');
  };

  const renderCard = (card, type, isSelected) => (
    <View style={[styles.card, isSelected && styles.selectedCard]}>
      <LinearGradient
        colors={type === 'image' ? ['#667eea', '#764ba2'] : ['#f093fb', '#f5576c']}
        style={styles.cardGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Text style={styles.cardTitle}>
          {type === 'image' ? card.name : card.word}
        </Text>
        <Text style={styles.cardDescription}>
          {type === 'image' ? card.description : card.meaning}
        </Text>
      </LinearGradient>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backText}>← 返回</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>OH卡心理投射</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.introCard}>
          <Text style={styles.introTitle}>🎭 OH卡的智慧</Text>
          <Text style={styles.introText}>
            OH卡通过图像与词语的随机组合，
            帮助你探索潜意识，获得新的洞察和启发。
          </Text>
        </View>

        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>你想探索什么主题？</Text>
          <TextInput
            style={styles.topicInput}
            placeholder="例如：我的人际关系、职业发展、内在成长..."
            value={topic}
            onChangeText={setTopic}
            multiline
          />
        </View>

        <TouchableOpacity
          style={[styles.drawButton, isDrawing && styles.drawingButton]}
          onPress={drawCards}
          disabled={isDrawing}
        >
          <Text style={styles.drawButtonText}>
            {isDrawing ? '正在抽取卡片...' : '开始抽卡'}
          </Text>
        </TouchableOpacity>

        {(selectedImageCard || selectedWordCard) && (
          <View style={styles.resultsSection}>
            <Text style={styles.resultsTitle}>你的卡片组合</Text>
            
            <View style={styles.cardsContainer}>
              {selectedImageCard && (
                <View style={styles.cardWrapper}>
                  <Text style={styles.cardType}>图像卡</Text>
                  {renderCard(selectedImageCard, 'image', true)}
                </View>
              )}
              
              {selectedWordCard && (
                <View style={styles.cardWrapper}>
                  <Text style={styles.cardType}>词语卡</Text>
                  {renderCard(selectedWordCard, 'word', true)}
                </View>
              )}
            </View>

            {interpretation && (
              <View style={styles.interpretationCard}>
                <Text style={styles.interpretationTitle}>✨ 解读与启发</Text>
                <Text style={styles.interpretationText}>{interpretation}</Text>
              </View>
            )}

            <TouchableOpacity style={styles.resetButton} onPress={resetCards}>
              <Text style={styles.resetButtonText}>重新开始</Text>
            </TouchableOpacity>
          </View>
        )}

        <View style={styles.tipsCard}>
          <Text style={styles.tipsTitle}>💡 使用建议</Text>
          <Text style={styles.tipText}>• 保持开放的心态，不要预设答案</Text>
          <Text style={styles.tipText}>• 关注第一直觉，它往往最准确</Text>
          <Text style={styles.tipText}>• 可以从不同角度解读卡片组合</Text>
          <Text style={styles.tipText}>• 记录你的感受和洞察</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  backButton: {
    padding: 8,
  },
  backText: {
    fontSize: 16,
    color: '#6366f1',
    fontWeight: '500',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  placeholder: {
    width: 60,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  introCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  introTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
    textAlign: 'center',
  },
  introText: {
    fontSize: 16,
    color: '#6b7280',
    lineHeight: 24,
    textAlign: 'center',
  },
  inputSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  topicInput: {
    borderWidth: 2,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  drawButton: {
    backgroundColor: '#6366f1',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 30,
  },
  drawingButton: {
    backgroundColor: '#9ca3af',
  },
  drawButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  resultsSection: {
    marginBottom: 30,
  },
  resultsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    textAlign: 'center',
    marginBottom: 20,
  },
  cardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  cardWrapper: {
    flex: 1,
    marginHorizontal: 8,
  },
  cardType: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 8,
  },
  card: {
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  selectedCard: {
    elevation: 6,
    shadowOpacity: 0.2,
  },
  cardGradient: {
    padding: 16,
    minHeight: 120,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 12,
    color: '#fff',
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: 16,
  },
  interpretationCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#6366f1',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  interpretationTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  interpretationText: {
    fontSize: 16,
    color: '#6b7280',
    lineHeight: 24,
  },
  resetButton: {
    backgroundColor: '#f3f4f6',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
  },
  tipsCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#10b981',
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
    marginBottom: 8,
  },
});
