import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

export default function ToolboxScreen({ navigation }) {
  const basicTools = [
    {
      id: 'tarot',
      title: '塔罗牌占卜',
      description: '探索内心深处的智慧',
      icon: '🃏',
      gradient: ['#667eea', '#764ba2'],
      onPress: () => navigation.navigate('TarotReading'),
    },
    {
      id: 'oracle',
      title: '神谕卡占卜',
      description: '接收宇宙的神圣指引',
      icon: '🔮',
      gradient: ['#f093fb', '#f5576c'],
      onPress: () => navigation.navigate('OracleReading'),
    },
    {
      id: 'oh-cards',
      title: 'OH卡心理投射',
      description: '深入了解潜意识世界',
      icon: '🎭',
      gradient: ['#4facfe', '#00f2fe'],
      onPress: () => navigation.navigate('OHCard'),
    },
  ];

  const diverseServices = [
    {
      id: 'numerology',
      title: '数字占卜',
      description: '解读数字中的生命密码',
      icon: '🔢',
      gradient: ['#fa709a', '#fee140'],
      onPress: () => navigation.navigate('Numerology'),
    },
    {
      id: 'life-code',
      title: '生命密码',
      description: '发现你的人生使命',
      icon: '🧬',
      gradient: ['#a8edea', '#fed6e3'],
      onPress: () => {},
    },
    {
      id: 'astrology',
      title: '占星学',
      description: '星空中的智慧指引',
      icon: '⭐',
      gradient: ['#ff9a9e', '#fecfef'],
      onPress: () => {},
    },
    {
      id: 'crystal',
      title: '水晶疗愈',
      description: '感受水晶的神奇能量',
      icon: '💎',
      gradient: ['#a18cd1', '#fbc2eb'],
      onPress: () => {},
    },
    {
      id: 'meditation',
      title: '冥想引导',
      description: '内在平静的修行之路',
      icon: '🧘‍♀️',
      gradient: ['#ffecd2', '#fcb69f'],
      onPress: () => {},
    },
  ];

  const renderToolCard = (tool, isBasic = false) => (
    <TouchableOpacity
      key={tool.id}
      style={[styles.toolCard, isBasic && styles.basicToolCard]}
      onPress={tool.onPress}
    >
      <LinearGradient
        colors={tool.gradient}
        style={styles.cardGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Text style={styles.toolIcon}>{tool.icon}</Text>
        <Text style={styles.toolTitle}>{tool.title}</Text>
        <Text style={styles.toolDescription}>{tool.description}</Text>
      </LinearGradient>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>工具箱</Text>
          <Text style={styles.headerSubtitle}>探索神秘学的无限可能</Text>
        </View>

        {/* 搜索栏 */}
        <View style={styles.searchContainer}>
          <Text style={styles.searchPlaceholder}>🔍 搜索占卜工具...</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>基础占卜工具</Text>
          <Text style={styles.sectionDescription}>
            经典的占卜工具，为你提供深度的内在洞察
          </Text>
          <View style={styles.toolsContainer}>
            {basicTools.map(tool => renderToolCard(tool, true))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>多元化身心灵服务</Text>
          <Text style={styles.sectionDescription}>
            丰富多样的神秘学服务，满足不同的探索需求
          </Text>
          <View style={styles.diverseToolsContainer}>
            {diverseServices.map(tool => renderToolCard(tool))}
          </View>
        </View>

        {/* 热门工具 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🔥 热门工具</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.hotToolsScrollView}
          >
            {[...basicTools, ...diverseServices].slice(0, 4).map((tool, index) => (
              <TouchableOpacity
                key={`hot-${tool.id}`}
                style={styles.hotToolCard}
                onPress={tool.onPress}
              >
                <LinearGradient
                  colors={tool.gradient}
                  style={styles.hotToolGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Text style={styles.hotToolIcon}>{tool.icon}</Text>
                  <Text style={styles.hotToolTitle}>{tool.title}</Text>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    backgroundColor: '#fff',
    marginBottom: 20,
  },
  searchContainer: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: '#f1f5f9',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  searchPlaceholder: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  section: {
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 20,
    lineHeight: 20,
  },
  toolsContainer: {
    gap: 15,
  },
  diverseToolsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 15,
  },
  toolCard: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  basicToolCard: {
    height: 120,
  },
  cardGradient: {
    padding: 20,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  toolIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  toolTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 4,
  },
  toolDescription: {
    fontSize: 14,
    color: '#fff',
    textAlign: 'center',
    opacity: 0.9,
  },
  hotToolsScrollView: {
    marginBottom: 10,
  },
  hotToolCard: {
    width: 120,
    height: 100,
    borderRadius: 12,
    overflow: 'hidden',
    marginRight: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  hotToolGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
  },
  hotToolIcon: {
    fontSize: 24,
    marginBottom: 6,
  },
  hotToolTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#fff',
    textAlign: 'center',
  },
});
