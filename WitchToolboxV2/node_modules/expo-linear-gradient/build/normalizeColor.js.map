{"version": 3, "file": "normalizeColor.js", "sourceRoot": "", "sources": ["../src/normalizeColor.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AACH,OAAO,EAAc,YAAY,EAAE,MAAM,cAAc,CAAC;AAExD,MAAM,UAAU,GAAG,CAAC,KAAa,EAAW,EAAE,CAC5C,KAAK,KAAK,cAAc;IACxB,KAAK,KAAK,cAAc;IACxB,KAAK,KAAK,SAAS;IACnB,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAE9B,MAAM,UAAU,cAAc,CAAC,KAAkB,EAAE,UAAkB,CAAC;IACpE,IAAI,KAAK,IAAI,IAAI;QAAE,OAAO;IAE1B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QACnD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;IACrC,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACjC,MAAM,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,GAAG,CAAC;QACjC,MAAM,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;QAChC,MAAM,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC;QACzB,MAAM,CAAC,GAAG,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACzC,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACvC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC;IACzC,CAAC;AACH,CAAC", "sourcesContent": ["/**\n * Copyright (c) Expo.\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { ColorValue, processColor } from 'react-native';\n\nconst isWebColor = (color: string): boolean =>\n  color === 'currentcolor' ||\n  color === 'currentColor' ||\n  color === 'inherit' ||\n  color.indexOf('var(') === 0;\n\nexport function normalizeColor(color?: ColorValue, opacity: number = 1): void | string {\n  if (color == null) return;\n\n  if (typeof color === 'string' && isWebColor(color)) {\n    return color;\n  }\n\n  const colorInt = processColor(color);\n  if (typeof colorInt === 'number') {\n    const r = (colorInt >> 16) & 255;\n    const g = (colorInt >> 8) & 255;\n    const b = colorInt & 255;\n    const a = ((colorInt >> 24) & 255) / 255;\n    const alpha = (a * opacity).toFixed(2);\n    return `rgba(${r},${g},${b},${alpha})`;\n  }\n}\n"]}