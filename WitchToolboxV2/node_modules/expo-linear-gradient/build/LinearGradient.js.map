{"version": 3, "file": "LinearGradient.js", "sourceRoot": "", "sources": ["../src/LinearGradient.tsx"], "names": [], "mappings": "AAAA,mCAAmC;AAEnC,YAAY,CAAC;AAEb,OAAO,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAClC,OAAO,EAAc,QAAQ,EAAE,YAAY,EAAa,MAAM,cAAc,CAAC;AAE7E,OAAO,oBAAoB,MAAM,wBAAwB,CAAC;AAqE1D;;GAEG;AACH,MAAM,OAAO,cAAe,SAAQ,SAA8B;IAChE,MAAM;QACJ,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;QACvE,IAAI,iBAAiB,GAAyC,SAAS,CAAC;QACxE,IAAI,SAAS,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAC;YAC9F,iBAAiB,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,CACL,CAAC,oBAAoB,CACnB,IAAI,KAAK,CAAC,CACV,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACtB,GAAG,EAAE,MAAa;gBAClB,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC;aAClC,CAAC,CAAC,CACH,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,CAC7C,SAAS,CAAC,CAAC,iBAAiB,CAAC,CAC7B,UAAU,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CACnC,QAAQ,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,EAC/B,CACH,CAAC;IACJ,CAAC;CACF;AAED,SAAS,eAAe,CACtB,KAAkC;IAElC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC/C,OAAO,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;QAC7F,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC", "sourcesContent": ["// Copyright © 2024 650 Industries.\n\n'use client';\n\nimport { Component } from 'react';\nimport { ColorValue, Platform, processColor, ViewProps } from 'react-native';\n\nimport NativeLinearGradient from './NativeLinearGradient';\nimport { NativeLinearGradientPoint } from './NativeLinearGradient.types';\n\n// @needsAudit\n/**\n * An object `{ x: number; y: number }` or array `[x, y]` that represents the point\n * at which the gradient starts or ends, as a fraction of the overall size of the gradient ranging\n * from `0` to `1`, inclusive.\n */\nexport type LinearGradientPoint =\n  | {\n      /**\n       * A number ranging from `0` to `1`, representing the position of gradient transformation.\n       */\n      x: number;\n      /**\n       * A number ranging from `0` to `1`, representing the position of gradient transformation.\n       */\n      y: number;\n    }\n  | NativeLinearGradientPoint;\n\n// @needsAudit\nexport type LinearGradientProps = ViewProps & {\n  /**\n   * A readonly array of colors that represent stops in the gradient. At least two colors are required\n   * (for a single-color background, use the `style.backgroundColor` prop on a `View` component).\n   *\n   * For TypeScript to know the provided array has 2 or more values, it should be provided \"inline\" or typed `as const`.\n   */\n  colors: readonly [ColorValue, ColorValue, ...ColorValue[]];\n  /**\n   * A readonly array that contains `number`s ranging from `0` to `1`, inclusive, and is the same length as the `colors` property.\n   * Each number indicates a color-stop location where each respective color should be located.\n   * If not specified, the colors will be distributed evenly across the gradient.\n   *\n   * For example, `[0.5, 0.8]` would render:\n   * - the first color, solid, from the beginning of the gradient view to 50% through (the middle);\n   * - a gradient from the first color to the second from the 50% point to the 80% point; and\n   * - the second color, solid, from the 80% point to the end of the gradient view.\n   *\n   * > The color-stop locations must be ascending from least to greatest.\n   * @default []\n   */\n  locations?: readonly [number, number, ...number[]] | null;\n  /**\n   * For example, `{ x: 0.1, y: 0.2 }` means that the gradient will start `10%` from the left and `20%` from the top.\n   *\n   * **On web**, this only changes the angle of the gradient because CSS gradients don't support changing the starting position.\n   * @default { x: 0.5, y: 0.0 }\n   */\n  start?: LinearGradientPoint | null;\n  /**\n   * For example, `{ x: 0.1, y: 0.2 }` means that the gradient will end `10%` from the left and `20%` from the bottom.\n   *\n   * **On web**, this only changes the angle of the gradient because CSS gradients don't support changing the end position.\n   * @default { x: 0.5, y: 1.0 }\n   */\n  end?: LinearGradientPoint | null;\n\n  /**\n   * Enables or disables paint dithering. Dithering can reduce the gradient color banding issue.\n   * Setting `false` may improve gradient rendering performance.\n   * @default true\n   * @platform android\n   */\n  dither?: boolean;\n};\n\n/**\n * Renders a native view that transitions between multiple colors in a linear direction.\n */\nexport class LinearGradient extends Component<LinearGradientProps> {\n  render() {\n    const { colors, locations, start, end, dither, ...props } = this.props;\n    let resolvedLocations: readonly number[] | null | undefined = locations;\n    if (locations && colors.length !== locations.length) {\n      console.warn('LinearGradient colors and locations props should be arrays of the same length');\n      resolvedLocations = locations.slice(0, colors.length);\n    }\n\n    return (\n      <NativeLinearGradient\n        {...props}\n        colors={Platform.select({\n          web: colors as any,\n          default: colors.map(processColor),\n        })}\n        dither={Platform.select({ android: dither })}\n        locations={resolvedLocations}\n        startPoint={_normalizePoint(start)}\n        endPoint={_normalizePoint(end)}\n      />\n    );\n  }\n}\n\nfunction _normalizePoint(\n  point?: LinearGradientPoint | null\n): NativeLinearGradientPoint | undefined {\n  if (!point) {\n    return undefined;\n  }\n\n  if (Array.isArray(point) && point.length !== 2) {\n    console.warn('start and end props for LinearGradient must be of the format [x,y] or {x, y}');\n    return undefined;\n  }\n\n  return Array.isArray(point) ? point : [point.x, point.y];\n}\n\nexport { NativeLinearGradientPoint };\n"]}