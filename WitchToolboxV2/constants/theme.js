// 女巫工具箱主题配置

export const Colors = {
  // 主色调 - 神秘紫色系
  primary: {
    50: '#f5f3ff',
    100: '#ede9fe',
    200: '#ddd6fe',
    300: '#c4b5fd',
    400: '#a78bfa',
    500: '#8b5cf6',
    600: '#7c3aed',
    700: '#6d28d9',
    800: '#5b21b6',
    900: '#4c1d95',
  },

  // 辅助色调 - 神秘蓝色系
  secondary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },

  // 中性色
  neutral: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
  },

  // 功能色
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6',

  // 渐变色组合
  gradients: {
    mystical: ['#667eea', '#764ba2'],
    aurora: ['#f093fb', '#f5576c'],
    cosmic: ['#4facfe', '#00f2fe'],
    sunset: ['#fa709a', '#fee140'],
    spring: ['#a8edea', '#fed6e3'],
    golden: ['#ffecd2', '#fcb69f'],
    ocean: ['#667eea', '#764ba2'],
    forest: ['#134e5e', '#71b280'],
    fire: ['#ff9a9e', '#fecfef'],
    earth: ['#a18cd1', '#fbc2eb'],
  },

  // 背景色
  background: {
    primary: '#f8fafc',
    secondary: '#ffffff',
    card: '#ffffff',
    modal: 'rgba(0, 0, 0, 0.5)',
  },

  // 文本色
  text: {
    primary: '#1f2937',
    secondary: '#6b7280',
    tertiary: '#9ca3af',
    inverse: '#ffffff',
    accent: '#8b5cf6',
  },

  // 边框色
  border: {
    light: '#e2e8f0',
    medium: '#cbd5e1',
    dark: '#94a3b8',
  },
};

export const Typography = {
  // 字体大小
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
  },

  // 字体粗细
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },

  // 行高
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
};

export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
};

export const BorderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,
};

export const Shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 10,
  },
};

// 动画配置
export const Animations = {
  timing: {
    fast: 200,
    normal: 300,
    slow: 500,
  },
  easing: {
    easeInOut: 'ease-in-out',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
  },
};

// 组件样式预设
export const ComponentStyles = {
  // 按钮样式
  button: {
    primary: {
      backgroundColor: Colors.primary[600],
      paddingVertical: Spacing.md,
      paddingHorizontal: Spacing.lg,
      borderRadius: BorderRadius.lg,
      ...Shadows.md,
    },
    secondary: {
      backgroundColor: Colors.neutral[100],
      paddingVertical: Spacing.md,
      paddingHorizontal: Spacing.lg,
      borderRadius: BorderRadius.lg,
      borderWidth: 1,
      borderColor: Colors.border.light,
    },
    ghost: {
      backgroundColor: 'transparent',
      paddingVertical: Spacing.md,
      paddingHorizontal: Spacing.lg,
      borderRadius: BorderRadius.lg,
      borderWidth: 1,
      borderColor: Colors.primary[600],
    },
  },

  // 卡片样式
  card: {
    default: {
      backgroundColor: Colors.background.card,
      borderRadius: BorderRadius.xl,
      padding: Spacing.lg,
      ...Shadows.md,
    },
    elevated: {
      backgroundColor: Colors.background.card,
      borderRadius: BorderRadius.xl,
      padding: Spacing.lg,
      ...Shadows.lg,
    },
    flat: {
      backgroundColor: Colors.background.card,
      borderRadius: BorderRadius.xl,
      padding: Spacing.lg,
      borderWidth: 1,
      borderColor: Colors.border.light,
    },
  },

  // 输入框样式
  input: {
    default: {
      borderWidth: 2,
      borderColor: Colors.border.light,
      borderRadius: BorderRadius.lg,
      paddingVertical: Spacing.md,
      paddingHorizontal: Spacing.md,
      fontSize: Typography.fontSize.base,
      color: Colors.text.primary,
    },
    focused: {
      borderColor: Colors.primary[600],
    },
    error: {
      borderColor: Colors.error,
    },
  },
};

// 导出默认主题
export const Theme = {
  colors: Colors,
  typography: Typography,
  spacing: Spacing,
  borderRadius: BorderRadius,
  shadows: Shadows,
  animations: Animations,
  components: ComponentStyles,
};
